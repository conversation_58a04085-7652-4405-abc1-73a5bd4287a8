<div class="iata-notification">
	@for (notification of notifications; track notification) {
		<div class="iata-notification__notification-item">
			<div class="row">
				<div class="col-11 iata-notification__notifyTime">
					{{ notification.createTime }}
				</div>
				<div class="col-1">
					@if (notification.hasRead) {
						<mat-icon color="primary">visibility</mat-icon>
					} @else {
						<mat-icon> visibility</mat-icon>
					}
				</div>
			</div>
			<div class="row">
				<div class="col-12">
					@if (notification.eventType === eventTypeEnum.LOGISTICS_OBJECT_UPDATED) {
						<span
							class="iata-notification__object"
							(click)="goToDetail(notification)"
							(keydown.enter)="$event.stopPropagation()"
							>{{ notification.dataType + '#' + notification.id }}</span
						>
						{{ 'notifications.updated' | translate }}
					} @else if (notification.eventType === eventTypeEnum.LOGISTICS_EVENT_RECEIVED) {
						<span class="iata-notification__company">{{ notification.companyName ?? '' }}</span>
						<span
							class="iata-notification__object"
							(click)="goToDetail(notification)"
							(keydown.enter)="$event.stopPropagation()"
							>{{ notification.dataType + '#' + notification.waybillNumber }}</span
						>
						<span>{{ 'notifications.event.content' | translate }}</span>
					}
				</div>
			</div>
		</div>
	}
	@if (hasPagination) {
		<mat-paginator [length]="totalRecords" [pageSizeOptions]="tablePageSizes" (page)="onPageChange($event)"> </mat-paginator>
	}
</div>
@if (dataLoading) {
	<iata-spinner></iata-spinner>
}
