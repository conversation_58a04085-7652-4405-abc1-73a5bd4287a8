import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubscriptionConfigurationDetailComponent } from './subscription-configuration-detail.component';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { DictObj, SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { of, throwError } from 'rxjs';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';

describe('SubscriptionConfigurationDetailComponent', () => {
	let component: SubscriptionConfigurationDetailComponent;
	let fixture: ComponentFixture<SubscriptionConfigurationDetailComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let dialogRefSpy: jasmine.SpyObj<MatDialogRef<SubscriptionConfigurationListObj>>;

	let formBuilder: FormBuilder;
	const mockDetail: SubscriptionConfigurationListObj = {
		id: '',
		topic: 'news',
		topicType: 'email',
		subscriptionType: '',
		subscriptionEventType: '[{"eventName": "login", "checked": "true"},{"eventName": "logout", "checked": "false"}]',
		description: '',
		expiresAt: '',
		userId: '',
		orgId: '',
		subscriberId: '',
		createAt: '',
		subscriptionRequestUri: '',
	};

	const mockEventTypes: DictObj[] = [
		{ code: 'login', name: 'User Login' },
		{ code: 'logout', name: 'User Logout' },
		{ code: 'update', name: 'Object Update' },
	];

	beforeEach(async () => {
		dialogRefSpy = jasmine.createSpyObj('MatDialogRef', ['close', 'afterClosed']);
		dialogRefSpy.afterClosed.and.returnValue(of(true));
		mockSubscriptionService = jasmine.createSpyObj('SubscriptionConfigurationService', [
			'getTopicTypeOptions',
			'getTopicOptions',
			'saveConfiguration',
			'getEventTypeOptions',
		]);
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.getTopicOptions.and.returnValue(of([]));
		mockSubscriptionService.getEventTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.saveConfiguration.and.returnValue(of(mockDetail));
		await TestBed.configureTestingModule({
			imports: [SubscriptionConfigurationDetailComponent, ReactiveFormsModule, TranslateModule.forRoot()],
			providers: [
				FormBuilder,
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionService },
				{ provide: MAT_DIALOG_DATA, useValue: mockDetail },
				{ provide: MatDialogRef, useValue: dialogRefSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionConfigurationDetailComponent);
		formBuilder = TestBed.inject(FormBuilder);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('buildEventTypeFormGroup', () => {
		it('should dynamically add controls to subscriptionEventType group', () => {
			component.buildEventTypeFormGroup(mockEventTypes);

			const eventsGroup = component.configurationForm.get('subscriptionEventType') as FormGroup;
			expect(eventsGroup).toBeTruthy();
			expect(eventsGroup.contains('login')).toBe(true);
			expect(eventsGroup.contains('logout')).toBe(true);
			expect(eventsGroup.contains('update')).toBe(true);
			expect(eventsGroup.get('login')?.value).toBe(false);
		});
	});

	describe('atLeastOneChecked validator', () => {
		let validatorFn: any;
		let eventsGroup: FormGroup;

		beforeEach(() => {
			validatorFn = component.atLeastOneChecked();
			eventsGroup = formBuilder.group({
				login: [false],
				logout: [false],
			});
		});

		it('should return null when at least one is checked', () => {
			eventsGroup.patchValue({ login: true });
			const result = validatorFn(eventsGroup);
			expect(result).toBeNull();
		});

		it('should return error when none is checked', () => {
			const result = validatorFn(eventsGroup);
			expect(result).toEqual({ atLeastOne: true });
		});
	});

	describe('getConfiguration', () => {
		it('should patch form values and set event type checks correctly', () => {
			component.buildEventTypeFormGroup([
				{ code: 'login', name: 'login' },
				{ code: 'logout', name: 'logout' },
			]);

			component.getConfiguration(mockDetail);

			const eventsGroup = component.configurationForm.get('subscriptionEventType') as FormGroup;

			expect(component.configurationForm.get('topicType')?.value).toBe('email');
			expect(component.configurationForm.get('topic')?.value).toBe('news');
			expect(eventsGroup.get('login')?.value).toBe(true);
			expect(eventsGroup.get('logout')?.value).toBe(false);
		});

		it('should do nothing if detail is falsy', () => {
			const spy = spyOn(component.configurationForm, 'patchValue');
			component.getConfiguration(null!);
			expect(spy).not.toHaveBeenCalled();
		});
	});

	describe('saveConfiguration', () => {
		beforeEach(() => {
			// Set up a valid form
			component.configurationForm = formBuilder.group({
				topicType: ['email'],
				topic: ['news'],
				subscriptionEventType: formBuilder.group({
					login: [true],
				}),
				description: ['test description'],
				expiresAt: ['2024-12-31'],
			});
		});

		it('should mark all as touched if form is invalid', () => {
			// Create an invalid form with proper validators
			component.configurationForm = formBuilder.group({
				topicType: ['', [Validators.required]],
				topic: ['', [Validators.required]],
				subscriptionEventType: formBuilder.group({}),
				description: ['', [Validators.required]],
				expiresAt: ['', [Validators.required]],
			});

			const spy = spyOn(component.configurationForm, 'markAllAsTouched');

			component.saveConfiguration();

			expect(spy).toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
		});

		it('should save configuration successfully when form is valid', () => {
			spyOn(component, 'getFormData').and.returnValue({
				topic: 'news',
				topicType: 'email',
				subscriptionEventType: [{ eventName: 'login', checked: 'true' }],
				description: 'test description',
				subscriptionType: '',
				expiresAt: '2024-12-31',
				userId: '',
				subscriberId: '',
				createAt: '',
			});

			component.saveConfiguration();

			expect(mockSubscriptionService.saveConfiguration).toHaveBeenCalled();
			expect(dialogRefSpy.close).toHaveBeenCalledWith(true);
			expect(component.dataLoading).toBe(false);
		});

		it('should handle save configuration error', () => {
			mockSubscriptionService.saveConfiguration.and.returnValue(throwError(() => new Error('Save failed')));
			spyOn(component, 'getFormData').and.returnValue({
				topic: 'news',
				topicType: 'email',
				subscriptionEventType: [{ eventName: 'login', checked: 'true' }],
				description: 'test description',
				subscriptionType: '',
				expiresAt: '2024-12-31',
				userId: '',
				subscriberId: '',
				createAt: '',
			});

			component.saveConfiguration();

			expect(mockSubscriptionService.saveConfiguration).toHaveBeenCalled();
			expect(dialogRefSpy.close).not.toHaveBeenCalled();
			expect(component.dataLoading).toBe(false);
		});
	});

	describe('getFormData', () => {
		beforeEach(() => {
			// Reset the form to a clean state
			component.configurationForm = formBuilder.group({
				topicType: ['email'],
				topic: ['test-topic'],
				subscriptionEventType: formBuilder.group({}),
				description: ['test description'],
				expiresAt: ['2024-12-31'],
			});

			// Add event type controls
			const eventsGroup = component.configurationForm.get('subscriptionEventType') as FormGroup;
			eventsGroup.addControl('login', new FormControl(true));
			eventsGroup.addControl('logout', new FormControl(false));
		});

		it('should return correct form data without id for new configuration', () => {
			component.data = null as any;

			const result = component.getFormData();

			expect(result).toEqual({
				topic: 'test-topic',
				topicType: 'email',
				subscriptionEventType: [
					{ eventName: 'login', checked: 'true' },
					{ eventName: 'logout', checked: 'false' },
				],
				description: 'test description',
				subscriptionType: '',
				expiresAt: '2024-12-31',
				userId: '',
				subscriberId: '',
				createAt: '',
			});
			expect(result.id).toBeUndefined();
		});

		it('should return correct form data with id for existing configuration', () => {
			component.data = { ...mockDetail, id: 'test-id' };

			const result = component.getFormData();

			expect(result).toEqual({
				id: 'test-id',
				topic: 'test-topic',
				topicType: 'email',
				subscriptionEventType: [
					{ eventName: 'login', checked: 'true' },
					{ eventName: 'logout', checked: 'false' },
				],
				description: 'test description',
				subscriptionType: '',
				expiresAt: '2024-12-31',
				userId: '',
				subscriberId: '',
				createAt: '',
			});
		});

		it('should handle empty subscriptionEventType', () => {
			// Set up form with null subscriptionEventType
			component.configurationForm = formBuilder.group({
				topicType: ['email'],
				topic: ['test-topic'],
				subscriptionEventType: [null],
				description: ['test description'],
				expiresAt: ['2024-12-31'],
			});

			const result = component.getFormData();

			expect(result.subscriptionEventType).toEqual([]);
		});
	});

	describe('ngOnInit', () => {
		it('should initialize options and form for new configuration', () => {
			const mockTopicOptions = ['topic1', 'topic2'];
			const mockTopicTypeOptions = [
				{ code: 'email', name: 'Email' },
				{ code: 'sms', name: 'SMS' },
			];

			// Create a fresh component instance for this test
			const freshFixture = TestBed.createComponent(SubscriptionConfigurationDetailComponent);
			const freshComponent = freshFixture.componentInstance;
			freshComponent.data = null as any;

			mockSubscriptionService.getTopicOptions.and.returnValue(of(mockTopicOptions));
			mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(mockTopicTypeOptions));
			mockSubscriptionService.getEventTypeOptions.and.returnValue(of(mockEventTypes));

			spyOn(freshComponent, 'buildEventTypeFormGroup');
			spyOn(freshComponent, 'getConfiguration');

			freshComponent.ngOnInit();

			expect(mockSubscriptionService.getTopicOptions).toHaveBeenCalled();
			expect(mockSubscriptionService.getTopicTypeOptions).toHaveBeenCalled();
			expect(mockSubscriptionService.getEventTypeOptions).toHaveBeenCalled();
			expect(freshComponent.topicOptions).toEqual(mockTopicOptions);
			expect(freshComponent.topicTypeOptions).toEqual(mockTopicTypeOptions);
			expect(freshComponent.eventTypeOptions).toEqual(mockEventTypes);
			expect(freshComponent.buildEventTypeFormGroup).toHaveBeenCalledWith(mockEventTypes);
			expect(freshComponent.isEdit).toBe(false);
			expect(freshComponent.getConfiguration).not.toHaveBeenCalled();
		});

		it('should initialize options and form for edit configuration', () => {
			const mockTopicOptions = ['topic1', 'topic2'];
			const mockTopicTypeOptions = [
				{ code: 'email', name: 'Email' },
				{ code: 'sms', name: 'SMS' },
			];

			// Create a fresh component instance for this test with data
			const freshFixture = TestBed.createComponent(SubscriptionConfigurationDetailComponent);
			const freshComponent = freshFixture.componentInstance;
			freshComponent.data = mockDetail;

			mockSubscriptionService.getTopicOptions.and.returnValue(of(mockTopicOptions));
			mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(mockTopicTypeOptions));
			mockSubscriptionService.getEventTypeOptions.and.returnValue(of(mockEventTypes));

			spyOn(freshComponent, 'buildEventTypeFormGroup');
			spyOn(freshComponent, 'getConfiguration');

			freshComponent.ngOnInit();

			expect(mockSubscriptionService.getTopicOptions).toHaveBeenCalled();
			expect(mockSubscriptionService.getTopicTypeOptions).toHaveBeenCalled();
			expect(mockSubscriptionService.getEventTypeOptions).toHaveBeenCalled();
			expect(freshComponent.topicOptions).toEqual(mockTopicOptions);
			expect(freshComponent.topicTypeOptions).toEqual(mockTopicTypeOptions);
			expect(freshComponent.eventTypeOptions).toEqual(mockEventTypes);
			expect(freshComponent.buildEventTypeFormGroup).toHaveBeenCalledWith(mockEventTypes);
			expect(freshComponent.isEdit).toBe(true);
			expect(freshComponent.getConfiguration).toHaveBeenCalledWith(mockDetail);
		});

		it('should not build form group when eventTypeOptions is falsy', () => {
			// Create a fresh component instance for this test
			const freshFixture = TestBed.createComponent(SubscriptionConfigurationDetailComponent);
			const freshComponent = freshFixture.componentInstance;

			mockSubscriptionService.getTopicOptions.and.returnValue(of([]));
			mockSubscriptionService.getTopicTypeOptions.and.returnValue(of([]));
			mockSubscriptionService.getEventTypeOptions.and.returnValue(of(null as any));

			spyOn(freshComponent, 'buildEventTypeFormGroup');
			spyOn(freshComponent, 'getConfiguration');

			freshComponent.ngOnInit();

			expect(freshComponent.buildEventTypeFormGroup).not.toHaveBeenCalled();
			expect(freshComponent.getConfiguration).not.toHaveBeenCalled();
		});
	});
});
