import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubscriptionRequestService } from '../../services/subscription-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { of } from 'rxjs';
import { SubscriptionListObj } from '../../models/subscription.model';
import { TranslateModule } from '@ngx-translate/core';
import { SubscriptionRequestListComponent } from './subscription-request-list.component';
import { ChangeDetectorRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { SubscriptionRequestDetailsComponent } from '../subscription-request-details/subscription-request-details.component';

// Mock ChangeDetectorRef
const mockCdr = {
	detectChanges: jasmine.createSpy('detectChanges'),
};

// Mock MatDialog
const mockDialogRef = {
	afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(true)),
};

const mockDialog = {
	open: jasmine.createSpy('open').and.returnValue(mockDialogRef),
};

describe('SubscriptionRequestListComponent', () => {
	let component: SubscriptionRequestListComponent;
	let fixture: ComponentFixture<SubscriptionRequestListComponent>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionRequestService>;
	let mockMatDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		mockSubscriptionService = jasmine.createSpyObj<SubscriptionRequestService>('SubscriptionService', [
			'loadAllData',
			'getDataPerPage',
		]);
		const mockRes: SubscriptionListObj[] = [
			{
				topic: '111',
				topicType: '111',
				createDate: '',
				approvedDate: '',
				subscriptionType: '',
				status: '',
				isRequestedBy: '',
				isRequestedAt: '',
				subscriberOrgName: '',
				orgId: '',
				id: '',
				subscriberOrgId: '',
				publisherOrgId: '',
			},
		];
		mockSubscriptionService.getDataPerPage.and.returnValue(of({ total: 55, rows: mockRes }));
		mockOrgMgmtRequestService = jasmine.createSpyObj<OrgMgmtRequestService>('OrgMgmtRequestService', ['getOrgList']);
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of([{ id: '111', name: '111', orgType: '1111' }]));
		mockMatDialog = mockDialog as jasmine.SpyObj<MatDialog>;

		await TestBed.configureTestingModule({
			imports: [SubscriptionRequestListComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
				{ provide: SubscriptionRequestService, useValue: mockSubscriptionService },
				{ provide: ChangeDetectorRef, useValue: mockCdr },
				{ provide: MatDialog, useValue: mockMatDialog },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SubscriptionRequestListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize orgMap with org list on ngOnInit', () => {
		fixture.detectChanges();

		expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
		expect(component.orgMap.size).toBe(1);
		expect(component.orgMap.get('111')).toBe('111');
	});

	it('should not call detectChanges if other input changes (not param)', () => {
		component.ngOnChanges({
			otherInput: {
				isFirstChange: () => true,
				previousValue: undefined,
				currentValue: 'new',
				firstChange: true,
			},
		} as any);

		expect(mockCdr.detectChanges).not.toHaveBeenCalled();
	});

	it('should handle empty org list', () => {
		fixture.detectChanges();

		expect(component.orgMap.size).toBe(1);
	});

	describe('ngOnChanges', () => {
		it('should call detectChanges when param input changes', () => {
			// Spy on the private cdr property
			const cdrSpy = spyOn(component['cdr'], 'detectChanges');

			component.ngOnChanges({
				param: {
					isFirstChange: () => false,
					previousValue: { fromTab: true },
					currentValue: { fromTab: false },
					firstChange: false,
				},
			} as any);

			expect(cdrSpy).toHaveBeenCalled();
		});

		it('should not call detectChanges when param is not changed', () => {
			const cdrSpy = spyOn(component['cdr'], 'detectChanges');

			component.ngOnChanges({
				otherProperty: {
					isFirstChange: () => false,
					previousValue: 'old',
					currentValue: 'new',
					firstChange: false,
				},
			} as any);

			expect(cdrSpy).not.toHaveBeenCalled();
		});

		it('should handle empty changes object', () => {
			const cdrSpy = spyOn(component['cdr'], 'detectChanges');

			component.ngOnChanges({});

			expect(cdrSpy).not.toHaveBeenCalled();
		});
	});

	describe('ngOnInit', () => {
		it('should handle null response from getOrgList', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(null as any));
			component.orgMap.clear();

			component.ngOnInit();

			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
			expect(component.orgMap.size).toBe(0);
		});

		it('should handle empty org list response', () => {
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of([]));
			component.orgMap.clear();

			component.ngOnInit();

			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
			expect(component.orgMap.size).toBe(0);
		});

		it('should populate orgMap with multiple organizations', () => {
			const mockOrgs = [
				{ id: 'org1', name: 'Organization 1', orgType: 'type1' },
				{ id: 'org2', name: 'Organization 2', orgType: 'type2' },
				{ id: 'org3', name: 'Organization 3', orgType: 'type3' },
			];
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockOrgs));
			component.orgMap.clear();

			component.ngOnInit();

			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
			expect(component.orgMap.size).toBe(3);
			expect(component.orgMap.get('org1')).toBe('Organization 1');
			expect(component.orgMap.get('org2')).toBe('Organization 2');
			expect(component.orgMap.get('org3')).toBe('Organization 3');
		});
	});

	describe('openRequestDetailDialog', () => {
		let mockRow: SubscriptionListObj;

		beforeEach(() => {
			mockRow = {
				topic: 'test-topic',
				topicType: 'test-type',
				createDate: '2023-01-01',
				approvedDate: '2023-01-02',
				subscriptionType: 'test-subscription',
				status: 'pending',
				isRequestedBy: 'requester-id',
				isRequestedAt: 'requested-at-id',
				subscriberOrgName: 'Test Org',
				orgId: 'org-123',
				id: 'sub-123',
				subscriberOrgId: 'subscriber-123',
				publisherOrgId: 'publisher-123',
			};
			component.orgMap.set('org1', 'Organization 1');
			component.orgMap.set('org2', 'Organization 2');
		});

		it('should open dialog with correct configuration and data', () => {
			component.openRequestDetailDialog(mockRow);

			expect(mockMatDialog.open).toHaveBeenCalledWith(SubscriptionRequestDetailsComponent, {
				width: '65vw',
				data: {
					id: 'sub-123',
					status: 'pending',
					subscriberOrgId: 'subscriber-123',
					publisherOrgId: 'publisher-123',
					orgMap: component.orgMap,
				},
			});
		});

		it('should update param when dialog closes with true result', () => {
			const originalParam = { ...component.param };
			mockDialogRef.afterClosed.and.returnValue(of(true));

			component.openRequestDetailDialog(mockRow);

			expect(component.param).toEqual({ ...originalParam });
			expect(component.param).not.toBe(originalParam); // Should be a new object reference
		});

		it('should not update param when dialog closes with false result', () => {
			const originalParam = component.param;
			mockDialogRef.afterClosed.and.returnValue(of(false));

			component.openRequestDetailDialog(mockRow);

			expect(component.param).toBe(originalParam); // Should be the same object reference
		});

		it('should not update param when dialog closes with null result', () => {
			const originalParam = component.param;
			mockDialogRef.afterClosed.and.returnValue(of(null));

			component.openRequestDetailDialog(mockRow);

			expect(component.param).toBe(originalParam); // Should be the same object reference
		});

		it('should not update param when dialog closes with undefined result', () => {
			const originalParam = component.param;
			mockDialogRef.afterClosed.and.returnValue(of(undefined));

			component.openRequestDetailDialog(mockRow);

			expect(component.param).toBe(originalParam); // Should be the same object reference
		});
	});

	describe('Column Transform Functions', () => {
		beforeEach(() => {
			component.orgMap.set('org1', 'Organization 1');
			component.orgMap.set('org2', 'Organization 2');
		});

		it('should transform subscriberOrgId using orgMap', () => {
			const subscriberOrgIdColumn = component.columns.find((col) => col.key === 'subscriberOrgId');

			expect(subscriberOrgIdColumn).toBeDefined();
			expect(subscriberOrgIdColumn!.transform!('org1')).toBe('Organization 1');
			expect(subscriberOrgIdColumn!.transform!('org2')).toBe('Organization 2');
		});

		it('should return original value when subscriberOrgId not found in orgMap', () => {
			const subscriberOrgIdColumn = component.columns.find((col) => col.key === 'subscriberOrgId');

			expect(subscriberOrgIdColumn).toBeDefined();
			expect(subscriberOrgIdColumn!.transform!('unknown-org')).toBe('unknown-org');
		});

		it('should transform isRequestedAt using formatDateTime', () => {
			const isRequestedAtColumn = component.columns.find((col) => col.key === 'isRequestedAt');

			expect(isRequestedAtColumn).toBeDefined();
			expect(isRequestedAtColumn!.transform!('2023-01-01T12:30:45.123Z')).toBe('2023-01-01 12:30:45');
			expect(isRequestedAtColumn!.transform!('2023-12-31T23:59:59.999Z')).toBe('2023-12-31 23:59:59');
		});

		it('should return empty string when isRequestedAt is falsy', () => {
			const isRequestedAtColumn = component.columns.find((col) => col.key === 'isRequestedAt');

			expect(isRequestedAtColumn).toBeDefined();
			expect(isRequestedAtColumn!.transform!('')).toBe('');
			expect(isRequestedAtColumn!.transform!(null)).toBe('');
			expect(isRequestedAtColumn!.transform!(undefined)).toBe('');
		});

		it('should handle null/undefined values in transform functions', () => {
			const subscriberOrgIdColumn = component.columns.find((col) => col.key === 'subscriberOrgId');
			const isRequestedAtColumn = component.columns.find((col) => col.key === 'isRequestedAt');

			expect(subscriberOrgIdColumn!.transform!(null as any)).toBe(null);
			expect(subscriberOrgIdColumn!.transform!(undefined as any)).toBe(undefined);

			expect(isRequestedAtColumn!.transform!(null as any)).toBe('');
			expect(isRequestedAtColumn!.transform!(undefined as any)).toBe('');
		});

		it('should call openRequestDetailDialog when status column cell is clicked', () => {
			const statusColumn = component.columns.find((col) => col.key === 'status');
			const mockRow: SubscriptionListObj = {
				topic: 'test-topic',
				topicType: 'test-type',
				createDate: '2023-01-01',
				approvedDate: '2023-01-02',
				subscriptionType: 'test-subscription',
				status: 'pending',
				isRequestedBy: 'requester-id',
				isRequestedAt: 'requested-at-id',
				subscriberOrgName: 'Test Org',
				orgId: 'org-123',
				id: 'sub-123',
				subscriberOrgId: 'subscriber-123',
				publisherOrgId: 'publisher-123',
			};

			spyOn(component, 'openRequestDetailDialog');

			expect(statusColumn).toBeDefined();
			expect(statusColumn!.clickCell).toBeDefined();

			statusColumn!.clickCell!(mockRow);

			expect(component.openRequestDetailDialog).toHaveBeenCalledWith(mockRow);
		});
	});

	describe('Component Properties', () => {
		it('should have correct default param value', () => {
			expect(component.param).toEqual({ fromTab: true });
		});

		it('should initialize orgMap as Map instance', () => {
			expect(component.orgMap).toBeInstanceOf(Map);
		});

		it('should have correct number of columns defined', () => {
			expect(component.columns).toBeDefined();
			expect(component.columns.length).toBe(6);
		});

		it('should have all required column keys', () => {
			const columnKeys = component.columns.map((col) => col.key);
			const expectedKeys = ['subscriberOrgId', 'topicType', 'topic', 'subscriberOrgName', 'isRequestedAt', 'status'];

			expect(columnKeys).toEqual(expectedKeys);
		});

		it('should have correct column headers', () => {
			const expectedHeaders = [
				'subscription.request.table.subscriber',
				'subscription.request.table.topicType',
				'subscription.request.table.topic',
				'subscription.request.table.requestBy',
				'subscription.request.table.requestAt',
				'subscription.request.table.status',
			];

			component.columns.forEach((column, index) => {
				expect(column.header).toBe(expectedHeaders[index]);
			});
		});
	});
});
