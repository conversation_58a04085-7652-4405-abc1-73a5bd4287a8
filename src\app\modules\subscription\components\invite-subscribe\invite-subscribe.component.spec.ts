import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InviteSubscribeComponent } from './invite-subscribe.component';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { Organization } from '@shared/models/organization.model';
import { SubscriptionConfigurationService } from '../../services/subscription-configuration.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { DictObj, SubscriptionConfigurationListObj } from '../../models/subscription.model';
import { TranslateModule } from '@ngx-translate/core';

describe('InviteSubscribeComponent', () => {
	let component: InviteSubscribeComponent;
	let fixture: ComponentFixture<InviteSubscribeComponent>;
	let mockSubscriptionService: jasmine.SpyObj<SubscriptionConfigurationService>;
	let mockOrgMgmtRequestService: jasmine.SpyObj<OrgMgmtRequestService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		mockSubscriptionService = jasmine.createSpyObj('SubscriptionService', [
			'getTopicTypeOptions',
			'getTopicOptions',
			'inviteToSubscribe',
		]);

		mockOrgMgmtRequestService = jasmine.createSpyObj<OrgMgmtRequestService>('OrgMgmtRequestService', ['getOrgInfo', 'getOrgList']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of([{ id: '111', name: '111', orgType: '1111' }]));

		await TestBed.configureTestingModule({
			imports: [InviteSubscribeComponent, MatDialogModule, ReactiveFormsModule, TranslateModule.forRoot()],
			providers: [
				NonNullableFormBuilder,
				{ provide: SubscriptionConfigurationService, useValue: mockSubscriptionService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: OrgMgmtRequestService, useValue: mockOrgMgmtRequestService },
				{ provide: MatDialogRef, useValue: { close: jasmine.createSpy('close') } },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(InviteSubscribeComponent);
		component = fixture.componentInstance;

		// Mock service responses
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of([]));
		mockSubscriptionService.getTopicOptions.and.returnValue(of([]));
		const mockResponse: SubscriptionConfigurationListObj = {
			id: '1',
			topic: 'test-topic',
			topicType: 'TEST_TYPE',
			subscriptionType: 'type1',
			subscriptionEventType: 'event1',
			description: 'test description',
			expiresAt: '2024-12-31',
			userId: 'user1',
			orgId: 'org1',
			subscriberId: 'subscriber1',
			createAt: '2024-01-01',
			subscriptionRequestUri: 'uri1',
		};
		mockSubscriptionService.inviteToSubscribe.and.returnValue(of(mockResponse));

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should load data on init', () => {
		const testData: DictObj[] = [{ name: '1', code: 'Test' }];
		const topics: string[] = ['111', '222'];
		const testData1: Organization[] = [{ id: '1', name: 'Test', orgType: 'test', prefix: 'test' }];
		mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(testData));
		mockSubscriptionService.getTopicOptions.and.returnValue(of(topics));
		mockOrgMgmtRequestService.getOrgList.and.returnValue(of(testData1));

		component.ngOnInit();

		expect(mockSubscriptionService.getTopicTypeOptions).toHaveBeenCalled();
		expect(mockSubscriptionService.getTopicOptions).toHaveBeenCalled();
		expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
	});

	it('should call inviteSbuScriber with identifyTopic when type is LOGISTICS_OBJECT_IDENTIFIER', () => {
		const logisticsType = 'https://api#LOGISTICS_OBJECT_IDENTIFIER';
		component.inviteForm.patchValue({
			topicType: logisticsType,
			topic: 'test-topic',
			subscribers: ['test-subscriber'],
		});

		component.inviteToSubscribe();

		expect(mockSubscriptionService.inviteToSubscribe).toHaveBeenCalledWith('test-topic', logisticsType, ['test-subscriber']);
	});

	it('should call inviteSbuScriber with selectTopic when type is not LOGISTICS_OBJECT_IDENTIFIER', () => {
		component.inviteForm.patchValue({
			topicType: 'OTHER_TYPE',
			topic: 'regular-topic',
			subscribers: ['test-subscriber'],
		});

		component.inviteToSubscribe();

		expect(mockSubscriptionService.inviteToSubscribe).toHaveBeenCalledWith('regular-topic', 'OTHER_TYPE', ['test-subscriber']);
	});

	describe('loadData', () => {
		it('should load topic types, topics, and partners', () => {
			const mockTopicTypes: DictObj[] = [{ name: 'Type1', code: 'T1' }];
			const mockTopics: string[] = ['topic1', 'topic2'];
			const mockPartners: Organization[] = [{ id: '1', name: 'Partner1', orgType: 'type1', prefix: 'P1' }];

			mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(mockTopicTypes));
			mockSubscriptionService.getTopicOptions.and.returnValue(of(mockTopics));
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockPartners));

			component.loadData();

			expect(mockSubscriptionService.getTopicTypeOptions).toHaveBeenCalled();
			expect(mockSubscriptionService.getTopicOptions).toHaveBeenCalled();
			expect(mockOrgMgmtRequestService.getOrgList).toHaveBeenCalled();
			expect(component.topicTypes).toEqual(mockTopicTypes);
			expect(component.topics).toEqual(mockTopics);
			expect(component.partners).toEqual(mockPartners);
		});
	});

	describe('inviteToSubscribe', () => {
		it('should mark form as touched and return early when form is invalid', () => {
			spyOn(component.inviteForm, 'markAllAsTouched');
			component.inviteForm.patchValue({
				topicType: '',
				topic: '',
				subscribers: [],
			});

			component.inviteToSubscribe();

			expect(component.inviteForm.markAllAsTouched).toHaveBeenCalled();
			expect(mockSubscriptionService.inviteToSubscribe).not.toHaveBeenCalled();
		});

		it('should set dataLoading to false and close dialog on successful invitation', () => {
			const mockDialogRef = TestBed.inject(MatDialogRef);
			const mockResponse: SubscriptionConfigurationListObj = {
				id: '1',
				topic: 'test-topic',
				topicType: 'TEST_TYPE',
				subscriptionType: 'type1',
				subscriptionEventType: 'event1',
				description: 'test description',
				expiresAt: '2024-12-31',
				userId: 'user1',
				orgId: 'org1',
				subscriberId: 'subscriber1',
				createAt: '2024-01-01',
				subscriptionRequestUri: 'uri1',
			};

			component.dataLoading = true;
			component.inviteForm.patchValue({
				topicType: 'TEST_TYPE',
				topic: 'test-topic',
				subscribers: ['subscriber1'],
			});

			mockSubscriptionService.inviteToSubscribe.and.returnValue(of(mockResponse));

			component.inviteToSubscribe();

			expect(mockSubscriptionService.inviteToSubscribe).toHaveBeenCalledWith('test-topic', 'TEST_TYPE', ['subscriber1']);
			expect(component.dataLoading).toBeFalse();
			expect(mockDialogRef.close).toHaveBeenCalled();
		});

		it('should set dataLoading to false and close dialog on invitation error', () => {
			const mockDialogRef = TestBed.inject(MatDialogRef);
			component.dataLoading = true;
			component.inviteForm.patchValue({
				topicType: 'TEST_TYPE',
				topic: 'test-topic',
				subscribers: ['subscriber1'],
			});

			mockSubscriptionService.inviteToSubscribe.and.returnValue(throwError(() => new Error('Test error')));

			component.inviteToSubscribe();

			expect(component.dataLoading).toBeFalse();
			expect(mockDialogRef.close).toHaveBeenCalled();
		});
	});

	describe('Form Validation', () => {
		it('should have required validators on all form controls', () => {
			expect(component.inviteForm.get('topicType')?.hasError('required')).toBeTruthy();
			expect(component.inviteForm.get('topic')?.hasError('required')).toBeTruthy();
			expect(component.inviteForm.get('subscribers')?.hasError('required')).toBeTruthy();
		});

		it('should be valid when all required fields are filled', () => {
			component.inviteForm.patchValue({
				topicType: 'TEST_TYPE',
				topic: 'test-topic',
				subscribers: ['subscriber1'],
			});

			expect(component.inviteForm.valid).toBeTruthy();
		});

		it('should be invalid when topicType is empty', () => {
			component.inviteForm.patchValue({
				topicType: '',
				topic: 'test-topic',
				subscribers: ['subscriber1'],
			});

			expect(component.inviteForm.invalid).toBeTruthy();
			expect(component.inviteForm.get('topicType')?.hasError('required')).toBeTruthy();
		});

		it('should be invalid when topic is empty', () => {
			component.inviteForm.patchValue({
				topicType: 'TEST_TYPE',
				topic: '',
				subscribers: ['subscriber1'],
			});

			expect(component.inviteForm.invalid).toBeTruthy();
			expect(component.inviteForm.get('topic')?.hasError('required')).toBeTruthy();
		});

		it('should be invalid when subscribers array is empty', () => {
			component.inviteForm.patchValue({
				topicType: 'TEST_TYPE',
				topic: 'test-topic',
				subscribers: [],
			});

			expect(component.inviteForm.invalid).toBeTruthy();
			expect(component.inviteForm.get('subscribers')?.hasError('required')).toBeTruthy();
		});
	});

	describe('Component Properties', () => {
		it('should initialize with default values', () => {
			expect(component.topicTypes).toBeDefined();
			expect(component.topics).toBeDefined();
			expect(component.partners).toBeDefined();
			expect(component.dataLoading).toBeFalse();
			expect(component.inviteForm).toBeDefined();

			// Verify the form structure
			expect(component.inviteForm.get('topicType')).toBeDefined();
			expect(component.inviteForm.get('topic')).toBeDefined();
			expect(component.inviteForm.get('subscribers')).toBeDefined();
		});

		it('should update properties when data is loaded', () => {
			const mockTopicTypes: DictObj[] = [{ name: 'Type1', code: 'T1' }];
			const mockTopics: string[] = ['topic1'];
			const mockPartners: Organization[] = [{ id: '1', name: 'Partner1', orgType: 'type1', prefix: 'P1' }];

			mockSubscriptionService.getTopicTypeOptions.and.returnValue(of(mockTopicTypes));
			mockSubscriptionService.getTopicOptions.and.returnValue(of(mockTopics));
			mockOrgMgmtRequestService.getOrgList.and.returnValue(of(mockPartners));

			component.loadData();

			expect(component.topicTypes).toEqual(mockTopicTypes);
			expect(component.topics).toEqual(mockTopics);
			expect(component.partners).toEqual(mockPartners);
		});

		it('should have empty arrays before data is loaded', () => {
			// Create a fresh component without calling ngOnInit
			const freshFixture = TestBed.createComponent(InviteSubscribeComponent);
			const freshComponent = freshFixture.componentInstance;

			// Don't call detectChanges() to avoid triggering ngOnInit
			expect(freshComponent.topicTypes).toEqual([]);
			expect(freshComponent.topics).toEqual([]);
			expect(freshComponent.partners).toEqual([]);
			expect(freshComponent.dataLoading).toBeFalse();
		});
	});
});
