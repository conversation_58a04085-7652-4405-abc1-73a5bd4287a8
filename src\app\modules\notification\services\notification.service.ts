import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { ApiService } from '@shared/services/api.service';
import { Observable } from 'rxjs';
import { NotificationListObj } from '../models/notification.model';

@Injectable({
	providedIn: 'root',
})
export class NotificationService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getNotificationPerPage(pageParams: PaginationRequest, unread: boolean): Observable<PaginationResponse<NotificationListObj>> {
		return super.getData<PaginationResponse<NotificationListObj>>('notification', {
			...pageParams,
			hasRead: unread,
		});
	}
}
