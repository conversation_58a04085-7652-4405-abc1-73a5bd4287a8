import { TestBed } from '@angular/core/testing';
import { MawbStatusService } from './mawb-status.service';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { ResponseObj } from '@shared/models/response.model';
import {
	MAWBUpdateEventObj,
	MAWBEventObj,
	MAWBEventListObj,
	MawbStatusPageRequest,
	HawbStatusPageRequest,
	HAWBEventListObj,
	PieceEventListObj,
	StatusHistory,
} from '../models/mawb-event.model';
import { environment } from '@environments/environment';
import { PaginationResponse } from '@shared/models/pagination-response.model';

const baseUrl = environment.baseApi;

describe('MawbStatusService', () => {
	let service: MawbStatusService;
	let httpMock: HttpTestingController;

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [
				MawbStatusService,
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		});
		service = TestBed.inject(MawbStatusService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should send a POST request to update status', () => {
		const mockParam: MAWBUpdateEventObj = {
			status: 'MOP',
			updateTime: '',
			eventTimeType: '',
			mawbId: '',
			choseAllHAWB: false,
			hawbIdList: [],
		};

		const mockResponse: ResponseObj<string> = {
			code: 200,
			data: 'Status updated',
			msg: '',
		};

		service.updateEventStatus(mockParam).subscribe((response) => {
			expect(response).toEqual('Status updated');
		});

		const req = httpMock.expectOne(`${baseUrl}/event-management/update-choose-status`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(mockParam);
		req.flush(mockResponse);
	});

	describe('getEventList', () => {
		it('should send a POST request to get event list', () => {
			const mockParam: MAWBEventObj = {
				mawbId: 'test-mawb-123',
				choseAllHAWB: true,
				hawbIdList: [
					{
						hawbId: 'hawb1',
						choseAllPiece: true,
						pieceIdList: ['piece1', 'piece2'],
					},
					{
						hawbId: 'hawb2',
						choseAllPiece: false,
						pieceIdList: ['piece3'],
					},
				],
			};

			const mockResponse: ResponseObj<string[]> = {
				code: 200,
				data: ['MOP', 'DEP', 'ARR'],
				msg: '',
			};

			service.getEventList(mockParam).subscribe((response) => {
				expect(response).toEqual(['MOP', 'DEP', 'ARR']);
			});

			const req = httpMock.expectOne(`${baseUrl}/event-management/get-choose-status`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(mockParam);
			req.flush(mockResponse);
		});

		it('should handle empty event list response', () => {
			const mockParam: MAWBEventObj = {
				mawbId: 'test-mawb-123',
				choseAllHAWB: false,
				hawbIdList: [],
			};

			const mockResponse: ResponseObj<string[]> = {
				code: 200,
				data: [],
				msg: '',
			};

			service.getEventList(mockParam).subscribe((response) => {
				expect(response).toEqual([]);
			});

			const req = httpMock.expectOne(`${baseUrl}/event-management/get-choose-status`);
			expect(req.request.method).toBe('POST');
			req.flush(mockResponse);
		});
	});

	describe('getMawbStatus', () => {
		it('should send a GET request to get MAWB status', () => {
			const mockParam = { loId: 'mawb-123', type: 'mawb' };

			const mockResponse: ResponseObj<MAWBEventListObj> = {
				code: 200,
				data: {
					mawbId: 'mawb-123',
					latestStatus: 'DELIVERED',
					updateTime: '2025-01-01T12:00:00Z',
					orgName: 'Test Org',
					userName: 'Test User',
					updateBy: 'test-user',
					checked: false,
					opened: false,
					code: 'DEL',
					eventDate: '2025-01-01',
				},
				msg: '',
			};

			service.getMawbStatus(mockParam).subscribe((response) => {
				expect(response.mawbId).toBe('mawb-123');
				expect(response.latestStatus).toBe('DELIVERED');
				expect(response.orgName).toBe('Test Org');
			});

			const req = httpMock.expectOne((request) => {
				return (
					request.url.includes(`${baseUrl}/event-management/get-lo-latest-status`) &&
					request.params.get('loId') === 'mawb-123' &&
					request.params.get('type') === 'mawb'
				);
			});
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle null MAWB status response', () => {
			const mockParam = { loId: 'invalid-mawb', type: 'mawb' };

			const mockResponse: ResponseObj<MAWBEventListObj> = {
				code: 200,
				data: null as any,
				msg: 'No data found',
			};

			service.getMawbStatus(mockParam).subscribe((response) => {
				expect(response).toBeNull();
			});

			const req = httpMock.expectOne((request) => {
				return request.url.includes(`${baseUrl}/event-management/get-lo-latest-status`);
			});
			req.flush(mockResponse);
		});
	});

	describe('listHawbStatusByMawb', () => {
		it('should send a GET request to list HAWB status by MAWB', () => {
			const mockParam: MawbStatusPageRequest = {
				pageNum: 1,
				pageSize: 10,
				orderByColumn: 'updateTime',
				isAsc: 'desc',
				mawbId: 'mawb-123',
			};

			const mockResponse: ResponseObj<PaginationResponse<HAWBEventListObj>> = {
				code: 200,
				data: {
					rows: [
						{
							hawbId: 'hawb-1',
							hawbNumber: 'HAWB001',
							latestStatus: 'DELIVERED',
							updateTime: '2025-01-01T12:00:00Z',
							orgName: 'Test Org',
							userName: 'Test User',
							updateBy: 'test-user',
							checked: false,
							opened: false,
							pageNum: 1,
							noMorePieces: false,
							pieceStatusList: [],
						},
						{
							hawbId: 'hawb-2',
							hawbNumber: 'HAWB002',
							latestStatus: 'IN_TRANSIT',
							updateTime: '2025-01-01T10:00:00Z',
							orgName: 'Another Org',
							userName: 'Another User',
							updateBy: 'another-user',
							checked: false,
							opened: false,
							pageNum: 1,
							noMorePieces: false,
							pieceStatusList: [],
						},
					],
					total: 2,
				},
				msg: '',
			};

			service.listHawbStatusByMawb(mockParam).subscribe((response) => {
				expect(response.rows.length).toBe(2);
				expect(response.total).toBe(2);
				expect(response.rows[0].hawbId).toBe('hawb-1');
				expect(response.rows[1].hawbId).toBe('hawb-2');
			});

			const req = httpMock.expectOne((request) => {
				return (
					request.url.includes(`${baseUrl}/event-management/list-hawb-status-by-mawb`) &&
					request.params.get('pageNum') === '1' &&
					request.params.get('pageSize') === '10' &&
					request.params.get('mawbId') === 'mawb-123'
				);
			});
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle empty HAWB list response', () => {
			const mockParam: MawbStatusPageRequest = {
				pageNum: 1,
				pageSize: 10,
				orderByColumn: '',
				isAsc: 'asc',
				mawbId: 'empty-mawb',
			};

			const mockResponse: ResponseObj<PaginationResponse<HAWBEventListObj>> = {
				code: 200,
				data: {
					rows: [],
					total: 0,
				},
				msg: '',
			};

			service.listHawbStatusByMawb(mockParam).subscribe((response) => {
				expect(response.rows).toEqual([]);
				expect(response.total).toBe(0);
			});

			const req = httpMock.expectOne((request) => {
				return request.url.includes(`${baseUrl}/event-management/list-hawb-status-by-mawb`);
			});
			req.flush(mockResponse);
		});
	});

	describe('listPieceStatusByHawb', () => {
		it('should send a GET request to list piece status by HAWB', () => {
			const mockParam: HawbStatusPageRequest = {
				pageNum: 1,
				pageSize: 20,
				orderByColumn: 'updateTime',
				isAsc: 'desc',
				hawbId: 'hawb-123',
			};

			const mockResponse: ResponseObj<PaginationResponse<PieceEventListObj>> = {
				code: 200,
				data: {
					rows: [
						{
							pieceId: 'piece-1',
							latestStatus: 'DELIVERED',
							updateTime: '2025-01-01T12:00:00Z',
							orgName: 'Test Org',
							userName: 'Test User',
							updateBy: 'test-user',
							checked: false,
							opened: false,
						},
						{
							pieceId: 'piece-2',
							latestStatus: 'IN_TRANSIT',
							updateTime: '2025-01-01T10:00:00Z',
							orgName: 'Another Org',
							userName: 'Another User',
							updateBy: 'another-user',
							checked: false,
							opened: false,
						},
					],
					total: 2,
				},
				msg: '',
			};

			service.listPieceStatusByHawb(mockParam).subscribe((response) => {
				expect(response.rows.length).toBe(2);
				expect(response.total).toBe(2);
				expect(response.rows[0].pieceId).toBe('piece-1');
				expect(response.rows[1].pieceId).toBe('piece-2');
			});

			const req = httpMock.expectOne((request) => {
				return (
					request.url.includes(`${baseUrl}/event-management/list-piece-status-by-hawb`) &&
					request.params.get('pageNum') === '1' &&
					request.params.get('pageSize') === '20' &&
					request.params.get('hawbId') === 'hawb-123'
				);
			});
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle empty piece list response', () => {
			const mockParam: HawbStatusPageRequest = {
				pageNum: 1,
				pageSize: 10,
				orderByColumn: '',
				isAsc: 'asc',
				hawbId: 'empty-hawb',
			};

			const mockResponse: ResponseObj<PaginationResponse<PieceEventListObj>> = {
				code: 200,
				data: {
					rows: [],
					total: 0,
				},
				msg: '',
			};

			service.listPieceStatusByHawb(mockParam).subscribe((response) => {
				expect(response.rows).toEqual([]);
				expect(response.total).toBe(0);
			});

			const req = httpMock.expectOne((request) => {
				return request.url.includes(`${baseUrl}/event-management/list-piece-status-by-hawb`);
			});
			req.flush(mockResponse);
		});
	});

	describe('getStatusHistoryList', () => {
		it('should send a GET request to get status history list', () => {
			const loId = 'mawb-123';
			const type = 'mawb';

			const mockResponse: ResponseObj<StatusHistory[]> = {
				code: 200,
				data: [
					{
						latestStatus: 'DELIVERED',
						eventDate: '2025-01-01',
						orgName: 'Test Org',
						userName: 'Test User',
						eventTimeType: 'ACTUAL',
						partialEventIndicator: false,
						eventLoId: 'event-1',
					},
					{
						latestStatus: 'IN_TRANSIT',
						eventDate: '2024-12-31',
						orgName: 'Another Org',
						userName: 'Another User',
						eventTimeType: 'PLANNED',
						partialEventIndicator: true,
						eventLoId: 'event-2',
					},
				],
				msg: '',
			};

			service.getStatusHistoryList(loId, type).subscribe((response) => {
				expect(response.length).toBe(2);
				expect(response[0].latestStatus).toBe('DELIVERED');
				expect(response[0].eventTimeType).toBe('ACTUAL');
				expect(response[1].latestStatus).toBe('IN_TRANSIT');
				expect(response[1].partialEventIndicator).toBe(true);
			});

			const req = httpMock.expectOne((request) => {
				return (
					request.url.includes(`${baseUrl}/event-management/get-lo-history-status`) &&
					request.params.get('loId') === 'mawb-123' &&
					request.params.get('type') === 'mawb'
				);
			});
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle empty status history response', () => {
			const loId = 'empty-mawb';
			const type = 'mawb';

			const mockResponse: ResponseObj<StatusHistory[]> = {
				code: 200,
				data: [],
				msg: '',
			};

			service.getStatusHistoryList(loId, type).subscribe((response) => {
				expect(response).toEqual([]);
			});

			const req = httpMock.expectOne((request) => {
				return request.url.includes(`${baseUrl}/event-management/get-lo-history-status`);
			});
			req.flush(mockResponse);
		});

		it('should handle null status history response', () => {
			const loId = 'null-mawb';
			const type = 'mawb';

			const mockResponse: ResponseObj<StatusHistory[]> = {
				code: 200,
				data: null as any,
				msg: 'No history found',
			};

			service.getStatusHistoryList(loId, type).subscribe((response) => {
				expect(response).toBeNull();
			});

			const req = httpMock.expectOne((request) => {
				return request.url.includes(`${baseUrl}/event-management/get-lo-history-status`);
			});
			req.flush(mockResponse);
		});
	});

	describe('getAllEvents', () => {
		it('should send a GET request to get all events', () => {
			const mockResponse: ResponseObj<string[]> = {
				code: 200,
				data: ['MOP', 'DEP', 'ARR', 'DLV', 'RCS', 'FOH', 'RCF'],
				msg: '',
			};

			service.getAllEvents().subscribe((response) => {
				expect(response).toEqual(['MOP', 'DEP', 'ARR', 'DLV', 'RCS', 'FOH', 'RCF']);
				expect(response.length).toBe(7);
			});

			const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/statusCode`);
			expect(req.request.method).toBe('GET');
			req.flush(mockResponse);
		});

		it('should handle empty events response', () => {
			const mockResponse: ResponseObj<string[]> = {
				code: 200,
				data: [],
				msg: '',
			};

			service.getAllEvents().subscribe((response) => {
				expect(response).toEqual([]);
			});

			const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/statusCode`);
			req.flush(mockResponse);
		});

		it('should handle null events response', () => {
			const mockResponse: ResponseObj<string[]> = {
				code: 200,
				data: null as any,
				msg: 'No events available',
			};

			service.getAllEvents().subscribe((response) => {
				expect(response).toBeNull();
			});

			const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/statusCode`);
			req.flush(mockResponse);
		});
	});

	describe('updateEventStatus edge cases', () => {
		it('should handle update with all HAWB selected', () => {
			const mockParam: MAWBUpdateEventObj = {
				status: 'DELIVERED',
				updateTime: '2025-01-01T12:00:00Z',
				eventTimeType: 'ACTUAL',
				mawbId: 'mawb-123',
				choseAllHAWB: true,
				hawbIdList: [],
			};

			const mockResponse: ResponseObj<string> = {
				code: 200,
				data: 'All HAWB status updated successfully',
				msg: '',
			};

			service.updateEventStatus(mockParam).subscribe((response) => {
				expect(response).toEqual('All HAWB status updated successfully');
			});

			const req = httpMock.expectOne(`${baseUrl}/event-management/update-choose-status`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body.choseAllHAWB).toBe(true);
			req.flush(mockResponse);
		});

		it('should handle update with specific HAWB list', () => {
			const mockParam: MAWBUpdateEventObj = {
				status: 'IN_TRANSIT',
				updateTime: null,
				eventTimeType: 'PLANNED',
				mawbId: 'mawb-456',
				choseAllHAWB: false,
				hawbIdList: [
					{
						hawbId: 'hawb-1',
						choseAllPiece: false,
						pieceIdList: ['piece-1', 'piece-2'],
					},
				],
			};

			const mockResponse: ResponseObj<string> = {
				code: 200,
				data: 'Selected HAWB status updated',
				msg: '',
			};

			service.updateEventStatus(mockParam).subscribe((response) => {
				expect(response).toEqual('Selected HAWB status updated');
			});

			const req = httpMock.expectOne(`${baseUrl}/event-management/update-choose-status`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body.choseAllHAWB).toBe(false);
			expect(req.request.body.hawbIdList.length).toBe(1);
			req.flush(mockResponse);
		});
	});
});
