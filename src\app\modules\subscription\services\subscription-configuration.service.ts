import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { GenericTableService } from '@shared/services/table/orll-table.interface';
import {
	DictObj,
	SubscriptionConfigDetailObj,
	SubscriptionConfigRequest,
	SubscriptionConfigurationListObj,
} from '../models/subscription.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
	providedIn: 'root',
})
export class SubscriptionConfigurationService extends ApiService implements GenericTableService<SubscriptionConfigurationListObj> {
	constructor(http: HttpClient) {
		super(http);
	}

	getDataPerPage(param: SubscriptionConfigRequest): Observable<PaginationResponse<SubscriptionConfigurationListObj>> {
		return this.postData('user-subscriptions/list', param);
	}

	loadAllData(param: SubscriptionConfigRequest): Observable<SubscriptionConfigurationListObj[]> {
		return this.postData('user-subscriptions/list', param);
	}

	saveConfiguration(param: SubscriptionConfigDetailObj): Observable<SubscriptionConfigurationListObj> {
		if (param.id) {
			return this.updateData('user-subscriptions', param);
		}
		return this.postData('user-subscriptions', param);
	}

	getTopicTypeOptions(): Observable<DictObj[]> {
		return this.getData('sys-management/enums/topicType');
	}

	getTopicOptions(): Observable<string[]> {
		return this.getData('user-subscriptions/topic-type-list');
	}

	getEventTypeOptions(): Observable<DictObj[]> {
		return this.getData('sys-management/enums/subscriptionEventType');
	}

	deleteConfiguration(id: string): Observable<true> {
		return super.deleteData('user-subscriptions', { id });
	}

	inviteToSubscribe(topic: string, topicType: string, partnerOrgIdList: string[]): Observable<SubscriptionConfigurationListObj> {
		return super.postData<SubscriptionConfigurationListObj>('user-subscriptions/invite', { topic, topicType, partnerOrgIdList });
	}
}
