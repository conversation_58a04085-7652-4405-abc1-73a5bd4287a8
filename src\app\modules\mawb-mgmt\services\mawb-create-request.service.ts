import { Injectable } from '@angular/core';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { MawbCreateDto } from '../models/mawb-create.model';
import { HawbCreateDto } from '../../hawb-mgmt/models/hawb-create.model';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class MawbCreateRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getHawbDetail(hawbId: string) {
		return super.getData<HawbCreateDto>(`hawb-management/info`, {
			hawbId,
		});
	}

	getHawbDetailBatch(hawbId: string[]): Observable<HawbCreateDto[]> {
		return super.postData<HawbCreateDto[]>(`hawb-management/batchInfo`, hawbId);
	}

	getMawbDetail(mawbId: string) {
		return super.getData<MawbCreateDto>(`mawb-management/info`, {
			mawbId,
		});
	}

	createMawb(data: MawbCreateDto): Observable<string> {
		return super.postData<string>('mawb-management', data);
	}

	updateMawb(mawbId: string, data: MawbCreateDto) {
		return super.updateDataPatch<any>(`mawb-management`, { ...data, id: mawbId });
	}
}
