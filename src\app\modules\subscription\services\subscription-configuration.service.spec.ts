import { TestBed } from '@angular/core/testing';
import { SubscriptionConfigurationService } from './subscription-configuration.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { DictObj, SubscriptionConfigDetailObj, SubscriptionConfigurationListObj } from '../models/subscription.model';
import { environment } from '@environments/environment';
import { PaginationResponse } from '@shared/models/pagination-response.model';

const baseUrl = environment.baseApi;

describe('SubscriptionConfigurationService', () => {
	let service: SubscriptionConfigurationService;
	let httpMock: HttpTestingController;

	const mockPagedResponse: PaginationResponse<SubscriptionConfigurationListObj> = {
		total: 25,
		pageNum: 1,
		rows: [
			{
				topic: '111',
				topicType: 'Log',
				subscriptionType: '1222',
				orgId: '',
				id: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
			{
				topic: '222',
				topicType: 'Log',
				subscriptionType: '1222',
				orgId: '',
				id: '',
				subscriptionEventType: '',
				description: '',
				expiresAt: '',
				userId: '',
				subscriberId: '',
				createAt: '',
				subscriptionRequestUri: '',
			},
		],
	};
	const allData = [
		{
			topic: '111',
			topicType: 'Log',
			subscriptionType: '1222',
			orgId: '',
			id: '',
			subscriptionEventType: '',
			description: '',
			expiresAt: '',
			userId: '',
			subscriberId: '',
			createAt: '',
			subscriptionRequestUri: '',
		},
	];

	const detailData: SubscriptionConfigDetailObj = {
		topic: '111',
		topicType: 'Log',
		subscriptionType: '1222',
		subscriptionEventType: [],
		description: '',
		expiresAt: '',
		userId: '',
		subscriberId: '',
		createAt: '',
	};

	const editDetailData: SubscriptionConfigDetailObj = {
		id: '111',
		topic: 'eee',
		topicType: 'eeeee',
		subscriptionType: 'wwwww',
		subscriptionEventType: [],
		description: '',
		expiresAt: '',
		userId: '',
		subscriberId: '',
		createAt: '',
	};
	const detailDataRes: SubscriptionConfigurationListObj = {
		id: '',
		topic: '111',
		topicType: 'Log',
		subscriptionType: '1222',
		subscriptionEventType: '',
		description: '',
		expiresAt: '',
		userId: '',
		orgId: '',
		subscriberId: '',
		createAt: '',
		subscriptionRequestUri: '',
	};

	beforeEach(() => {
		TestBed.configureTestingModule({
			providers: [SubscriptionConfigurationService, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()],
		});
		service = TestBed.inject(SubscriptionConfigurationService);
		httpMock = TestBed.inject(HttpTestingController);
	});

	afterEach(() => {
		httpMock.verify();
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should getDataPerPaget and return SubscriptionConfigurationListObj[]', () => {
		const param = { description: 'all' };

		service.getDataPerPage(param).subscribe((data) => {
			expect(data).toEqual(mockPagedResponse);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(mockPagedResponse);
	});

	it('should loadData return SubscriptionListObj[]', () => {
		const param = { description: 'all' };

		service.loadAllData(param).subscribe((data) => {
			expect(data).toEqual(allData);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
		expect(req.request.method).toBe('POST');
		expect(req.request.body).toEqual(param);
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(allData);
	});

	it('should saveConfiguration for create and return  SubscriptionListObj', () => {
		service.saveConfiguration(detailData).subscribe((data) => {
			expect(data).toEqual(detailDataRes);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
		expect(req.request.method).toBe('POST');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(detailDataRes);
	});

	it('should saveConfiguration for update and return  SubscriptionListObj', () => {
		service.saveConfiguration(editDetailData).subscribe((data) => {
			expect(data).toEqual(detailDataRes);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
		expect(req.request.method).toBe('PUT');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(detailDataRes);
	});

	it('should getTopicTypeOptions and return  DictList', () => {
		const types: DictObj[] = [{ code: '111', name: '111' }];
		service.getTopicTypeOptions().subscribe((data) => {
			expect(data).toEqual(types);
		});

		const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/topicType`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(types);
	});

	it('should getTopicOptions and return  DictList', () => {
		const topics: string[] = ['111'];
		service.getTopicOptions().subscribe((data) => {
			expect(data).toEqual(topics);
		});

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/topic-type-list`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(topics);
	});

	it('should getEventTypes and return  DictList', () => {
		const types: DictObj[] = [{ code: '111', name: '111' }];
		service.getEventTypeOptions().subscribe((data) => {
			expect(data).toEqual(types);
		});

		const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/subscriptionEventType`);
		expect(req.request.method).toBe('GET');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

		req.flush(types);
	});

	it('should inviteSubscriber and return  DictList', () => {
		service.inviteToSubscribe('111', 'Log', ['11', '22']).subscribe();

		const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/invite`);
		expect(req.request.method).toBe('POST');
		expect(req.request.headers.get('Content-Type')).not.toBe('application/json');
	});

	describe('deleteConfiguration', () => {
		it('should delete configuration and return true', () => {
			const configId = 'test-config-123';

			service.deleteConfiguration(configId).subscribe((result) => {
				expect(result).toBe(true);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
			expect(req.request.method).toBe('DELETE');
			expect(req.request.body).toEqual({ id: configId });
			expect(req.request.headers.get('Content-Type')).not.toBe('application/json');

			req.flush(true);
		});

		it('should handle delete configuration with empty id', () => {
			const configId = '';

			service.deleteConfiguration(configId).subscribe((result) => {
				expect(result).toBe(true);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
			expect(req.request.method).toBe('DELETE');
			expect(req.request.body).toEqual({ id: '' });

			req.flush(true);
		});

		it('should handle delete configuration error', () => {
			const configId = 'test-config-123';
			const errorMessage = 'Configuration not found';

			service.deleteConfiguration(configId).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(404);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
			req.flush({ message: errorMessage }, { status: 404, statusText: 'Not Found' });
		});
	});

	describe('inviteToSubscribe Edge Cases', () => {
		it('should handle invite with empty partner list', () => {
			const mockResponse: SubscriptionConfigurationListObj = {
				id: 'invite-123',
				topic: '111',
				topicType: 'Log',
				subscriptionType: 'invitation',
				orgId: 'org-123',
				subscriptionEventType: '',
				description: 'Invitation sent',
				expiresAt: '2024-12-31',
				userId: 'user-123',
				subscriberId: 'subscriber-123',
				createAt: '2024-01-01',
				subscriptionRequestUri: 'http://example.com/invite',
			};

			service.inviteToSubscribe('111', 'Log', []).subscribe((result) => {
				expect(result).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/invite`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual({
				topic: '111',
				topicType: 'Log',
				partnerOrgIdList: [],
			});

			req.flush(mockResponse);
		});

		it('should handle invite with multiple partners', () => {
			const partnerList = ['org-1', 'org-2', 'org-3', 'org-4'];
			const mockResponse: SubscriptionConfigurationListObj = {
				id: 'invite-456',
				topic: 'multi-partner',
				topicType: 'Event',
				subscriptionType: 'invitation',
				orgId: 'org-123',
				subscriptionEventType: '',
				description: 'Multi-partner invitation',
				expiresAt: '2024-12-31',
				userId: 'user-123',
				subscriberId: 'subscriber-123',
				createAt: '2024-01-01',
				subscriptionRequestUri: 'http://example.com/invite',
			};

			service.inviteToSubscribe('multi-partner', 'Event', partnerList).subscribe((result) => {
				expect(result).toEqual(mockResponse);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/invite`);
			expect(req.request.body).toEqual({
				topic: 'multi-partner',
				topicType: 'Event',
				partnerOrgIdList: partnerList,
			});

			req.flush(mockResponse);
		});

		it('should handle invite error scenarios', () => {
			const errorMessage = 'Invalid partner organization';

			service.inviteToSubscribe('invalid-topic', 'InvalidType', ['invalid-org']).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(400);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/invite`);
			req.flush({ message: errorMessage }, { status: 400, statusText: 'Bad Request' });
		});
	});

	describe('Error Handling', () => {
		it('should handle network errors in getDataPerPage', () => {
			const param = { description: 'test' };

			service.getDataPerPage(param).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(0);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
			req.error(new ProgressEvent('Network error'));
		});

		it('should handle server errors in saveConfiguration', () => {
			const errorMessage = 'Internal server error';

			service.saveConfiguration(detailData).subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(500);
					expect(error.error.message).toContain(errorMessage);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
			req.flush({ message: errorMessage }, { status: 500, statusText: 'Internal Server Error' });
		});

		it('should handle unauthorized errors in getTopicTypeOptions', () => {
			service.getTopicTypeOptions().subscribe({
				next: () => fail('should have failed'),
				error: (error) => {
					expect(error.status).toBe(401);
				},
			});

			const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/topicType`);
			req.flush({ message: 'Unauthorized' }, { status: 401, statusText: 'Unauthorized' });
		});
	});

	describe('Edge Cases and Validation', () => {
		it('should handle saveConfiguration with complex event types', () => {
			const complexDetailData: SubscriptionConfigDetailObj = {
				topic: 'complex-topic',
				topicType: 'ComplexType',
				subscriptionType: 'premium',
				subscriptionEventType: [
					{ eventName: 'create', checked: 'true' },
					{ eventName: 'update', checked: 'false' },
					{ eventName: 'delete', checked: 'true' },
				],
				description: 'Complex subscription with multiple event types',
				expiresAt: '2025-12-31T23:59:59Z',
				userId: 'user-complex',
				subscriberId: 'subscriber-complex',
				createAt: '2024-01-01T00:00:00Z',
			};

			service.saveConfiguration(complexDetailData).subscribe((result) => {
				expect(result).toEqual(detailDataRes);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions`);
			expect(req.request.method).toBe('POST');
			expect(req.request.body).toEqual(complexDetailData);

			req.flush(detailDataRes);
		});

		it('should handle loadAllData with empty response', () => {
			const param = { description: 'empty' };

			service.loadAllData(param).subscribe((data) => {
				expect(data).toEqual([]);
				expect(Array.isArray(data)).toBeTruthy();
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/list`);
			req.flush([]);
		});

		it('should handle getTopicOptions with large response', () => {
			// eslint-disable-next-line @typescript-eslint/naming-convention
			const largeTopicList = Array.from({ length: 100 }, (_, i) => `topic-${i}`);

			service.getTopicOptions().subscribe((topics) => {
				expect(topics).toEqual(largeTopicList);
				expect(topics.length).toBe(100);
			});

			const req = httpMock.expectOne(`${baseUrl}/user-subscriptions/topic-type-list`);
			req.flush(largeTopicList);
		});

		it('should handle getEventTypeOptions with special characters', () => {
			const specialEventTypes: DictObj[] = [
				{ code: 'event@#$', name: 'Special Event with @#$ chars' },
				{ code: 'event_with_underscore', name: 'Event with underscore' },
				{ code: 'event-with-dash', name: 'Event with dash' },
			];

			service.getEventTypeOptions().subscribe((eventTypes) => {
				expect(eventTypes).toEqual(specialEventTypes);
				expect(eventTypes[0].code).toContain('@#$');
			});

			const req = httpMock.expectOne(`${baseUrl}/sys-management/enums/subscriptionEventType`);
			req.flush(specialEventTypes);
		});
	});
});
