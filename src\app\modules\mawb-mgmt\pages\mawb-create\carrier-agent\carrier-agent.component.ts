import { ChangeDetectionStrategy, Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Country } from 'src/app/modules/sli-mgmt/models/country.model';
import { Province } from 'src/app/modules/sli-mgmt/models/province.model';
import { CodeName } from '@shared/models/code-name.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliCreateRequestService } from 'src/app/modules/sli-mgmt/services/sli-create-request.service';
import { MatIconModule } from '@angular/material/icon';
import { OrgInfo } from '@shared/models/org-info.model';
import { ShipmentParty } from 'src/app/modules/sli-mgmt/models/shipment-party.model';
import { OrgType } from '@shared/models/org-type.model';

@Component({
	selector: 'orll-carrier-agent',
	imports: [MatFormFieldModule, MatInput, MatIconModule, ReactiveFormsModule, MatAutocompleteModule, TranslatePipe],
	templateUrl: './carrier-agent.component.html',
	styleUrl: './carrier-agent.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CarrierAgentComponent extends DestroyRefComponent implements OnInit, OnChanges {
	@Input() carrierInfo: OrgInfo | null = null;
	@Input() haveAccountNo = false;
	@Input() share = false;
	carrierAgentForm = new FormGroup({
		id: new FormControl<string | null>(null),
		company: new FormControl<string>('', [Validators.required]),
		agentIataCode: new FormControl<string>('', [Validators.pattern(/^\d{11}$/g)]),
		accountingNoteText: new FormControl<string>(''),
		country: new FormControl<string>('', [Validators.required]),
		province: new FormControl<string>('', [Validators.required]),
		cityCode: new FormControl<string>('', [Validators.required]),
		textualPostCode: new FormControl<string>(''),
		address: new FormControl<string>('', [Validators.required]),
		phoneNumber: new FormControl<string>(''),
		email: new FormControl<string>('', [Validators.email]),
	});

	countries: Country[] = [];
	provinces: Province[] = [];
	cities: CodeName[] = [];
	filteredCountries: Country[] = [];
	filteredProvinces: Province[] = [];
	filteredCities: CodeName[] = [];

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['carrierInfo']) {
			this.fillCarrierAgentInfo();
		}
		if (changes['share']) {
			Object.keys(this.carrierAgentForm.controls).forEach((key) => {
				if (key !== 'accountingNoteText') {
					const control = this.carrierAgentForm.get(key);
					changes['share'].currentValue ? control!.disable() : control!.enable();
				}
			});
		}
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCountries().subscribe((countries: Country[]) => {
			this.countries = countries;
			this.filteredCountries = countries;
		});
	}

	private isOrgInfo(info: OrgInfo | ShipmentParty): info is OrgInfo {
		return 'persons' in info;
	}

	private getShipmentInfo(info: OrgInfo | ShipmentParty): ShipmentParty {
		if (this.isOrgInfo(info)) {
			const contact = info.persons.find((person) => person.contactRole === OrgType.CUSTOMER_CONTACT);
			return {
				companyName: info.companyName,
				contactName: contact?.contactName ?? '',
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: contact?.phoneNumber ?? '',
				emailAddress: contact?.emailAddress ?? '',
				companyType: info.partyRole,
			};
		} else {
			return {
				companyName: info.companyName,
				contactName: info.contactName,
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: info.phoneNumber,
				emailAddress: info.emailAddress,
				companyType: info.companyType,
			};
		}
	}

	fillCarrierAgentInfo(): void {
		if (!this.carrierInfo) return;

		this.setupCountryValueChange(this.carrierInfo.countryCode);
		this.setupRegionValueChange(this.carrierInfo.regionCode);

		const carrierInfo = this.getShipmentInfo(this.carrierInfo);
		this.carrierAgentForm.patchValue({
			company: carrierInfo.companyName,
			agentIataCode: this.carrierInfo.iataCargoAgentCode,
			country: carrierInfo.countryCode,
			province: carrierInfo.regionCode,
			cityCode: carrierInfo.cityCode,
			textualPostCode: carrierInfo.textualPostCode,
			address: carrierInfo.locationName,
			phoneNumber: carrierInfo.phoneNumber,
			email: carrierInfo.emailAddress,
		});
	}

	private setupAutocomplete(): void {
		this.carrierAgentForm
			.get('country')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredCountries = this.countries.filter((country) =>
					country.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayCountryName = (code: string): string => {
		const country = this.countries.find((item) => item.code === code);
		return country?.name ?? '';
	};

	displayProvinceName = (code: string): string => {
		const province = this.provinces.find((item) => item.code === code);
		return province?.name ?? '';
	};

	displayCityName = (code: string): string => {
		const city = this.cities.find((item) => item.code === code);
		return city?.name ?? '';
	};

	private setupCountryValueChange(value: string): void {
		const selectedCountry: Country[] = this.countries.filter((country: Country) => country.code === value);
		this.sliCreateRequestService.getProvinces(selectedCountry[0]).subscribe((provinces: Province[]) => {
			this.provinces = provinces;
			this.filteredProvinces = provinces;

			this.carrierAgentForm
				.get('province')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredProvinces = this.provinces.filter((province) =>
						province.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	private setupRegionValueChange(value: string): void {
		const selectedProvince: Province[] = this.provinces.filter((province: Province) => province.code === value);
		this.sliCreateRequestService.getCities(selectedProvince[0]).subscribe((cities: CodeName[]) => {
			this.cities = cities;
			this.filteredCities = cities;

			this.carrierAgentForm
				.get('cityCode')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredCities = this.cities.filter((city) =>
						city.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	countryValueChange(event?: MatAutocompleteSelectedEvent): void {
		this.carrierAgentForm.get('province')?.setValue('');
		this.carrierAgentForm.get('cityCode')?.setValue('');

		const value = event?.option.value ?? '';
		this.setupCountryValueChange(value);
	}

	regionValueChange(event?: MatAutocompleteSelectedEvent): void {
		const value = event?.option.value ?? '';
		this.setupRegionValueChange(value);
	}

	getFormData(ignore?: boolean): (ShipmentParty & { iataCargoAgentCode: string }) | null {
		if (!ignore && this.carrierAgentForm.invalid) {
			return null;
		}
		return {
			id: this.carrierInfo?.id ?? null,
			companyName: this.carrierAgentForm.value.company!,
			contactName: '',
			countryCode: this.carrierAgentForm.value.country!,
			regionCode: this.carrierAgentForm.value.province!,
			cityCode: this.carrierAgentForm.value.cityCode!,
			textualPostCode: this.carrierAgentForm.value.textualPostCode ?? '',
			locationName: this.carrierAgentForm.value.address!,
			phoneNumber: this.carrierAgentForm.value.phoneNumber ?? '',
			emailAddress: this.carrierAgentForm.value.email ?? '',
			iataCargoAgentCode: this.carrierAgentForm.value.agentIataCode ?? '',
			companyType: OrgType.FORWARDER,
		};
	}
}
