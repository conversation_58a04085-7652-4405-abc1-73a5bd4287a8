import { TestBed } from '@angular/core/testing';
import { HawbSearchRequestService } from './hawb-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { HAWB_LIST } from '../ref-data/hawb-list.data';
import { HawbListObject } from '../models/hawb-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { HawbSearchPayload } from '../models/hawb-search-payload.model';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { environment } from '@environments/environment';
import { HawbCreateDto } from '../models/hawb-create.model';
import { PieceList } from '../../sli-mgmt/models/piece/piece-list.model';
import { SearchType } from '@shared/models/search-type.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

const baseUrl = environment.baseApi;

describe('HawbSearchRequestService', () => {
	let service: HawbSearchRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get', 'post', 'patch']);

		TestBed.configureTestingModule({
			providers: [HawbSearchRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(HawbSearchRequestService);
	});

	describe('getOptions', () => {
		it('should retrieve shipper organizations when id is "shipper"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'shipper';

			const mockApiResponse = ['Demo Shipper', 'Demo Shipper 2'];
			httpClientSpy.get.and.returnValue(of(mockApiResponse));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Demo Shipper');
					expect(response[0].name).toBe('Demo Shipper');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/get-keyword`, {
				params: jasmine.any(Object),
			});
		});

		it('should retrieve consignee organizations when id is "consignee"', (done: DoneFn) => {
			// Arrange
			const keyword = 'test';
			const id = SearchType.CONSIGNEE;

			const mockApiResponse = ['Demo Consignee', 'Demo Consignee 2'];
			httpClientSpy.get.and.returnValue(of(mockApiResponse));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Demo Consignee');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/get-keyword`, {
				params: jasmine.any(Object),
			});
		});

		it('should return empty array for default case', (done: DoneFn) => {
			// Arrange
			const keyword = 'test';
			const id = 'unknown';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});
	});

	describe('getHawbList', () => {
		it('should retrieve all SLI list items', (done: DoneFn) => {
			// Arrange
			const hawbSearchPayload: HawbSearchPayload = {};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<HawbListObject> = {
				rows: HAWB_LIST,
				total: HAWB_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getHawbList(paginationRequest, hawbSearchPayload).subscribe({
				next: (response: PaginationResponse<HawbListObject>) => {
					// Assert
					expect(response.rows.length).toBe(HAWB_LIST.length);
					expect(response.total).toBe(HAWB_LIST.length);
					expect(response.rows[0].hawbNumber).toBe(HAWB_LIST[0].hawbNumber);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const hawbSearchPayload: HawbSearchPayload = {
				hawbNumberList: ['H000920'],
				departureLocationList: ['DEP A'],
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<HawbListObject> = {
				rows: [HAWB_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getHawbList(paginationRequest, hawbSearchPayload).subscribe({
				next: (response: PaginationResponse<HawbListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].hawbNumber).toBe('H000920');
					expect(response.rows[0].origin).toBe('DEP A');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { params: jasmine.any(Object) });
		});
	});

	describe('getHawbDetail', () => {
		it('should retrieve HAWB detail', (done: DoneFn) => {
			// Arrange
			const hawbId = '123';
			const mockResponse: HawbCreateDto = {
				sliId: 'S001',
				orgId: 'O001',
				waybillPrefix: '123',
				waybillNumber: '4567890',
				partyList: [],
				accountingInformation: 'info',
				departureLocation: 'TPE',
				arrivalLocation: 'JFK',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				weightValuationIndicator: 'W',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 1000 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 1000 },
				textualHandlingInstructions: 'handle with care',
				totalGrossWeight: 100,
				rateClassCode: 'Q',
				totalVolumetricWeight: 120,
				rateCharge: { currencyUnit: 'USD', numericalValue: 500 },
				goodsDescriptionForRate: 'goods',
				otherChargeList: [],
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'TPE',
				consignorDeclarationSignature: 'shipper',
				carrierDeclarationSignature: 'carrier',
			};
			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getHawbDetail(hawbId).subscribe({
				next: (response: HawbCreateDto) => {
					// Assert
					expect(response.sliId).toBe('S001');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/info`, { params: jasmine.any(Object) });
		});
	});

	describe('getPieceList', () => {
		it('should retrieve piece list for a HAWB', (done: DoneFn) => {
			// Arrange
			const hawbId = '123';
			const paginationRequest: PaginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<PieceList> = {
				rows: [
					{
						type: 'type',
						pieceId: 'P001',
						productDescription: 'desc',
						packagingType: 'box',
						grossWeight: 10,
						dimensions: { length: 1, width: 1, height: 1, unit: 'cm' },
						pieceQuantity: 1,
						slac: 1,
					},
				],
				total: 1,
			};
			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getPieceList(paginationRequest, hawbId).subscribe({
				next: (response: PaginationResponse<PieceList>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].pieceId).toBe('P001');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/piece/list`, { params: jasmine.any(Object) });
		});
	});

	describe('getTotalPieceQuantity', () => {
		it('should retrieve total piece quantity for a HAWB', (done: DoneFn) => {
			// Arrange
			const hawbId = '123';
			const mockResponse = { totalQuantity: 10, totalSlac: 5, eventStatus: 'OK' };
			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getTotalPieceQuantity(hawbId).subscribe({
				next: (response) => {
					// Assert
					expect(response.totalQuantity).toBe(10);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/piece-summary`, {
				params: jasmine.any(Object),
			});
		});
	});

	describe('keywordQuery', () => {
		it('should perform a keyword query', (done: DoneFn) => {
			// Arrange
			const type = SearchType.SHIPPER;
			const keyword = 'test';
			const mockResponse: string[] = ['Test Shipper'];
			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.keywordQuery(type, keyword).subscribe({
				next: (response) => {
					// Assert
					expect(response.length).toBe(1);
					expect(response[0].name).toEqual(mockResponse[0]);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/hawb-management/get-keyword`, {
				params: jasmine.any(Object),
			});
		});
	});

	describe('createHawb', () => {
		it('should create a new HAWB', (done: DoneFn) => {
			// Arrange
			const sliNumber = 'S001';
			const hawbData: Omit<HawbCreateDto, 'sliId'> = {
				orgId: 'O001',
				waybillPrefix: '123',
				waybillNumber: '4567890',
				partyList: [],
				accountingInformation: 'info',
				departureLocation: 'TPE',
				arrivalLocation: 'JFK',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				weightValuationIndicator: 'W',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 1000 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 1000 },
				textualHandlingInstructions: 'handle with care',
				totalGrossWeight: 100,
				rateClassCode: 'Q',
				totalVolumetricWeight: 120,
				rateCharge: { currencyUnit: 'USD', numericalValue: 500 },
				goodsDescriptionForRate: 'goods',
				otherChargeList: [],
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'TPE',
				consignorDeclarationSignature: 'shipper',
				carrierDeclarationSignature: 'carrier',
			};
			const mockResponse = { id: '456' };
			httpClientSpy.post.and.returnValue(of(mockResponse));

			// Act
			service.createHawb(sliNumber, hawbData).subscribe({
				next: (response) => {
					// Assert
					expect(response.id).toBe('456');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.post).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { ...hawbData, sliId: sliNumber });
		});
	});

	describe('updateHawb', () => {
		it('should update an existing HAWB', (done: DoneFn) => {
			// Arrange
			const hawbId = '123';
			const sliId = 'S001';
			const orgId = 'O001';
			const hawbData: HawbCreateDto = {
				sliId: 'S001',
				orgId: 'O001',
				waybillPrefix: '123',
				waybillNumber: '4567890',
				partyList: [],
				accountingInformation: 'info',
				departureLocation: 'TPE',
				arrivalLocation: 'JFK',
				insuredAmount: { currencyUnit: 'USD', numericalValue: 1000 },
				weightValuationIndicator: 'W',
				otherChargesIndicator: 'N',
				declaredValueForCarriage: { currencyUnit: 'USD', numericalValue: 1000 },
				declaredValueForCustoms: { currencyUnit: 'USD', numericalValue: 1000 },
				textualHandlingInstructions: 'handle with care',
				totalGrossWeight: 100,
				rateClassCode: 'Q',
				totalVolumetricWeight: 120,
				rateCharge: { currencyUnit: 'USD', numericalValue: 500 },
				goodsDescriptionForRate: 'goods',
				otherChargeList: [],
				carrierDeclarationDate: '2024-01-01',
				carrierDeclarationPlace: 'TPE',
				consignorDeclarationSignature: 'shipper',
				carrierDeclarationSignature: 'carrier',
			};
			const mockResponse = { success: true };
			httpClientSpy.patch.and.returnValue(of(mockResponse));

			// Act
			service.updateHawb(hawbId, sliId, hawbData).subscribe({
				next: (response) => {
					// Assert
					expect(response.success).toBe(true);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.patch).toHaveBeenCalledWith(`${baseUrl}/hawb-management`, { ...hawbData, sliId, orgId, id: hawbId });
		});
	});
});
