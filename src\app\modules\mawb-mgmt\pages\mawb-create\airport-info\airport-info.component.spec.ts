import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AirportInfoComponent } from './airport-info.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { FormGroup, Validators } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { SliCreateRequestService } from '../../../../sli-mgmt/services/sli-create-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { of } from 'rxjs';
import { SimpleChanges } from '@angular/core';
import { CodeName } from '@shared/models/code-name.model';

describe('AirportInfoComponent', () => {
	let component: AirportInfoComponent;
	let fixture: ComponentFixture<AirportInfoComponent>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;
	const mockCurrencies = ['USD', 'EUR', 'GBP'];
	const mockAirports: CodeName[] = [
		{ code: 'LAX', name: 'Los Angeles International Airport' },
		{ code: 'JFK', name: 'John F. Kennedy International Airport' },
		{ code: 'LHR', name: 'London Heathrow Airport' },
	];
	const mockChargesCodes: CodeName[] = [
		{ code: 'CC', name: 'Carrier Charges' },
		{ code: 'AC', name: 'Agent Charges' },
		{ code: 'TC', name: 'Terminal Charges' },
	];

	beforeEach(async () => {
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getAirports']);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getEnumCode']);

		sliCreateRequestServiceSpy.getAirports.and.returnValue(of(mockAirports));
		orgMgmtRequestServiceSpy.getEnumCode.and.returnValue(of(mockChargesCodes));

		await TestBed.configureTestingModule({
			imports: [AirportInfoComponent],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(AirportInfoComponent);
		component = fixture.componentInstance;

		component.currencies = mockCurrencies;

		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with correct structure and validators', () => {
		const form = component.airportInfoForm;

		// Test required fields
		expect(form.get('departureAndRequestedRouting')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('airportOfDestination')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('wtOrVal')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('other')?.hasValidator(Validators.required)).toBeTruthy();

		// Test optional fields
		expect(form.get('flight')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('to')?.hasValidator(Validators.required)).toBeFalsy();
		expect(form.get('date')?.hasValidator(Validators.required)).toBeTruthy();

		// Test nested form groups
		expect(form.get('amountOfInsurance')).toBeInstanceOf(FormGroup);
		expect(form.get('declaredValueForCarriage')).toBeInstanceOf(FormGroup);
		expect(form.get('declaredValueForCustoms')).toBeInstanceOf(FormGroup);
	});

	it('should pass currencies to child currency components', () => {
		const currencyComponents = fixture.debugElement.queryAll(By.directive(CurrencyInputComponent));

		expect(currencyComponents.length).toBe(3); // amountOfInsurance + declaredValueForCarriage + declaredValueForCustoms

		currencyComponents.forEach((comp) => {
			expect(comp.componentInstance.currencies).toEqual(mockCurrencies);
		});
	});

	it('should validate declaredValueForCarriage numericalValue pattern', () => {
		const control = component.airportInfoForm.get('declaredValueForCarriage.numericalValue');

		// Valid values
		control?.setValue('NCV');
		expect(control?.valid).toBeTruthy();

		control?.setValue('100');
		expect(control?.valid).toBeTruthy();

		control?.setValue('123.45');
		expect(control?.valid).toBeTruthy();

		// Invalid values
		control?.setValue('INVALID');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('123.456');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('123.');
		expect(control?.invalid).toBeTruthy();
	});

	it('should validate declaredValueForCustoms numericalValue pattern', () => {
		const control = component.airportInfoForm.get('declaredValueForCustoms.numericalValue');

		// Valid values
		control?.setValue('NVD');
		expect(control?.valid).toBeTruthy();

		control?.setValue('200');
		expect(control?.valid).toBeTruthy();

		control?.setValue('456.78');
		expect(control?.valid).toBeTruthy();

		// Invalid values
		control?.setValue('INVALID');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('456.789');
		expect(control?.invalid).toBeTruthy();

		control?.setValue('456.');
		expect(control?.invalid).toBeTruthy();
	});

	it('should handle currency input changes', () => {
		const currencyComponents = fixture.debugElement.queryAll(By.directive(CurrencyInputComponent));

		// Test amountOfInsurance
		currencyComponents[0].componentInstance.currencyForm.patchValue({
			currencyUnit: 'EUR',
			numericalValue: '1000',
		});
		fixture.detectChanges();

		expect(component.airportInfoForm.get('amountOfInsurance.currencyUnit')?.value).toBe('EUR');
		expect(component.airportInfoForm.get('amountOfInsurance.numericalValue')?.value).toBe('1000');

		// Test declaredValueForCarriage
		currencyComponents[1].componentInstance.currencyForm.patchValue({
			currencyUnit: 'USD',
			numericalValue: 'NCV',
		});
		fixture.detectChanges();

		expect(component.airportInfoForm.get('declaredValueForCarriage.currencyUnit')?.value).toBe('USD');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.value).toBe('NCV');
	});

	it('should maintain form state when rebuilding', () => {
		// Set form values
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Test Route',
			airportOfDestination: 'Test Destination',
			flight: 'FL123',
			declaredValueForCarriage: {
				currencyUnit: 'GBP',
				numericalValue: 'NCV',
			},
		});

		// Rebuild component
		fixture = TestBed.createComponent(AirportInfoComponent);
		const newComponent = fixture.componentInstance;
		newComponent.currencies = mockCurrencies;
		fixture.detectChanges();

		// Reapply form values
		newComponent.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Test Route',
			airportOfDestination: 'Test Destination',
			flight: 'FL123',
			declaredValueForCarriage: {
				currencyUnit: 'GBP',
				numericalValue: 'NCV',
			},
		});

		fixture.detectChanges();

		// Verify state
		expect(newComponent.airportInfoForm.get('departureAndRequestedRouting')?.value).toBe('Test Route');
		expect(newComponent.airportInfoForm.get('declaredValueForCarriage.currencyUnit')?.value).toBe('GBP');
	});

	it('should handle special values NCV and NVD correctly', () => {
		// Set NCV for declaredValueForCarriage
		component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.setValue('NCV');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.valid).toBeTruthy();

		// Set NVD for declaredValueForCustoms
		component.airportInfoForm.get('declaredValueForCustoms.numericalValue')?.setValue('NVD');
		expect(component.airportInfoForm.get('declaredValueForCustoms.numericalValue')?.valid).toBeTruthy();

		// Set invalid special value
		component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.setValue('INVALID');
		expect(component.airportInfoForm.get('declaredValueForCarriage.numericalValue')?.invalid).toBeTruthy();
	});

	it('should validate all required fields as a group', () => {
		// Initially should be invalid
		expect(component.airportInfoForm.invalid).toBeTruthy();

		// Set required fields
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Route',
			airportOfDestination: 'Destination',
			wtOrVal: 'Weight',
			other: 'Other',
			flight: 'flight123',
			date: new Date(),
		});

		expect(component.airportInfoForm.valid).toBeTruthy();
	});

	it('should not require non-mandatory fields', () => {
		// Set only required fields
		component.airportInfoForm.patchValue({
			departureAndRequestedRouting: 'Route',
			airportOfDestination: 'Destination',
			wtOrVal: 'Weight',
			other: 'Other',
			flight: 'flight123',
			date: new Date(),
		});

		// Optional fields should be valid even when empty
		expect(component.airportInfoForm.get('to')?.valid).toBeTruthy();
		expect(component.airportInfoForm.get('amountOfInsurance')?.valid).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should enable flight-related controls when flightDisabled is false', () => {
			component.flightDisabled = false;
			component.ngOnInit();

			expect(component.airportInfoForm.get('flight')?.enabled).toBeTruthy();
			expect(component.airportInfoForm.get('to')?.enabled).toBeTruthy();
			expect(component.airportInfoForm.get('date')?.enabled).toBeTruthy();
			expect(component.airportInfoForm.get('byFirstCarrier')?.enabled).toBeTruthy();
		});

		it('should enable wtOrVal and other controls when flightDisabled is true', () => {
			component.flightDisabled = true;
			component.ngOnInit();

			expect(component.airportInfoForm.get('wtOrVal')?.enabled).toBeTruthy();
			expect(component.airportInfoForm.get('other')?.enabled).toBeTruthy();
		});

		it('should disable all controls when share is true', () => {
			component.share = true;
			component.ngOnInit();

			Object.keys(component.airportInfoForm.controls).forEach((key) => {
				expect(component.airportInfoForm.get(key)?.disabled).toBeTruthy();
			});
		});

		it('should load airports and charges codes', () => {
			component.ngOnInit();

			expect(sliCreateRequestServiceSpy.getAirports).toHaveBeenCalled();
			expect(orgMgmtRequestServiceSpy.getEnumCode).toHaveBeenCalled();
			expect(component.airports).toEqual(mockAirports);
			expect(component.allChargesCode).toEqual(mockChargesCodes);
		});
	});

	describe('ngOnChanges', () => {
		it('should disable controls when share changes to true', () => {
			const changes: SimpleChanges = {
				share: {
					currentValue: true,
					previousValue: false,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.ngOnChanges(changes);

			Object.keys(component.airportInfoForm.controls).forEach((key) => {
				expect(component.airportInfoForm.get(key)?.disabled).toBeTruthy();
			});
		});

		it('should enable controls when share changes to false', () => {
			const changes: SimpleChanges = {
				share: {
					currentValue: false,
					previousValue: true,
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			component.ngOnChanges(changes);

			Object.keys(component.airportInfoForm.controls).forEach((key) => {
				expect(component.airportInfoForm.get(key)?.enabled).toBeTruthy();
			});
		});

		it('should not affect controls when share is not in changes', () => {
			const changes: SimpleChanges = {
				currencies: {
					currentValue: ['USD'],
					previousValue: [],
					firstChange: false,
					isFirstChange: () => false,
				},
			};

			spyOn(component.airportInfoForm, 'get');
			component.ngOnChanges(changes);

			expect(component.airportInfoForm.get).not.toHaveBeenCalled();
		});
	});

	describe('displayAirportName', () => {
		beforeEach(() => {
			component.airports = mockAirports;
		});

		it('should return airport name for valid code', () => {
			const result = component.displayAirportName('LAX');
			expect(result).toBe('Los Angeles International Airport');
		});

		it('should return empty string for invalid code', () => {
			const result = component.displayAirportName('INVALID');
			expect(result).toBe('');
		});

		it('should return empty string for empty code', () => {
			const result = component.displayAirportName('');
			expect(result).toBe('');
		});
	});

	describe('displayChargesCodeName', () => {
		beforeEach(() => {
			component.allChargesCode = mockChargesCodes;
		});

		it('should return charges code name for valid code', () => {
			const result = component.displayChargesCodeName('CC');
			expect(result).toBe('Carrier Charges');
		});

		it('should return empty string for invalid code', () => {
			const result = component.displayChargesCodeName('INVALID');
			expect(result).toBe('');
		});

		it('should return empty string for empty code', () => {
			const result = component.displayChargesCodeName('');
			expect(result).toBe('');
		});
	});

	describe('filterAirports', () => {
		beforeEach(() => {
			component.airports = mockAirports;
		});

		it('should filter airports by code (case insensitive)', () => {
			const result = component.filterAirports('lax');
			expect(result).toEqual([{ code: 'LAX', name: 'Los Angeles International Airport' }]);
		});

		it('should return all airports for empty search', () => {
			const result = component.filterAirports('');
			expect(result).toEqual(mockAirports);
		});

		it('should return empty array for no matches', () => {
			const result = component.filterAirports('XYZ');
			expect(result).toEqual([]);
		});

		it('should handle partial matches', () => {
			const result = component.filterAirports('L');
			expect(result.length).toBe(2); // LAX and LHR
		});
	});

	describe('onToByChange', () => {
		let mockEvent: Event;

		beforeEach(() => {
			mockEvent = {
				target: { value: 'test-value' } as HTMLInputElement,
			} as unknown as Event;
		});

		describe('type t1 (to)', () => {
			it('should enable toBy2ndCarrier when value is provided', () => {
				spyOn(component.airportInfoForm.get('toBy2ndCarrier')!, 'enable');

				component.onToByChange(mockEvent, 't1');

				expect(component.airportInfoForm.get('toBy2ndCarrier')!.enable).toHaveBeenCalled();
			});

			it('should disable and clear toBy2ndCarrier and toBy3rdCarrier when value is empty', () => {
				const emptyEvent = {
					target: { value: '' } as HTMLInputElement,
				} as unknown as Event;

				spyOn(component.airportInfoForm.get('toBy2ndCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('toBy2ndCarrier')!, 'disable');
				spyOn(component.airportInfoForm.get('toBy3rdCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('toBy3rdCarrier')!, 'disable');

				component.onToByChange(emptyEvent, 't1');

				expect(component.airportInfoForm.get('toBy2ndCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('toBy2ndCarrier')!.disable).toHaveBeenCalled();
				expect(component.airportInfoForm.get('toBy3rdCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('toBy3rdCarrier')!.disable).toHaveBeenCalled();
			});
		});

		describe('type t2 (toBy2ndCarrier)', () => {
			it('should enable toBy3rdCarrier when value is provided', () => {
				spyOn(component.airportInfoForm.get('toBy3rdCarrier')!, 'enable');

				component.onToByChange(mockEvent, 't2');

				expect(component.airportInfoForm.get('toBy3rdCarrier')!.enable).toHaveBeenCalled();
			});

			it('should disable and clear toBy3rdCarrier when value is empty', () => {
				const emptyEvent = {
					target: { value: '' } as HTMLInputElement,
				} as unknown as Event;

				spyOn(component.airportInfoForm.get('toBy3rdCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('toBy3rdCarrier')!, 'disable');

				component.onToByChange(emptyEvent, 't2');

				expect(component.airportInfoForm.get('toBy3rdCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('toBy3rdCarrier')!.disable).toHaveBeenCalled();
			});
		});

		describe('type b1 (byFirstCarrier)', () => {
			it('should enable by2ndCarrier when value is provided', () => {
				spyOn(component.airportInfoForm.get('by2ndCarrier')!, 'enable');

				component.onToByChange(mockEvent, 'b1');

				expect(component.airportInfoForm.get('by2ndCarrier')!.enable).toHaveBeenCalled();
			});

			it('should disable and clear by2ndCarrier and by3rdCarrier when value is empty', () => {
				const emptyEvent = {
					target: { value: '' } as HTMLInputElement,
				} as unknown as Event;

				spyOn(component.airportInfoForm.get('by2ndCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('by2ndCarrier')!, 'disable');
				spyOn(component.airportInfoForm.get('by3rdCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('by3rdCarrier')!, 'disable');

				component.onToByChange(emptyEvent, 'b1');

				expect(component.airportInfoForm.get('by2ndCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('by2ndCarrier')!.disable).toHaveBeenCalled();
				expect(component.airportInfoForm.get('by3rdCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('by3rdCarrier')!.disable).toHaveBeenCalled();
			});
		});

		describe('type b2 (by2ndCarrier)', () => {
			it('should enable by3rdCarrier when value is provided', () => {
				spyOn(component.airportInfoForm.get('by3rdCarrier')!, 'enable');

				component.onToByChange(mockEvent, 'b2');

				expect(component.airportInfoForm.get('by3rdCarrier')!.enable).toHaveBeenCalled();
			});

			it('should disable and clear by3rdCarrier when value is empty', () => {
				const emptyEvent = {
					target: { value: '' } as HTMLInputElement,
				} as unknown as Event;

				spyOn(component.airportInfoForm.get('by3rdCarrier')!, 'patchValue');
				spyOn(component.airportInfoForm.get('by3rdCarrier')!, 'disable');

				component.onToByChange(emptyEvent, 'b2');

				expect(component.airportInfoForm.get('by3rdCarrier')!.patchValue).toHaveBeenCalledWith('');
				expect(component.airportInfoForm.get('by3rdCarrier')!.disable).toHaveBeenCalled();
			});
		});

		describe('default case', () => {
			it('should do nothing for unknown type', () => {
				spyOn(component.airportInfoForm, 'get');

				component.onToByChange(mockEvent, 'unknown');

				expect(component.airportInfoForm.get).not.toHaveBeenCalled();
			});
		});
	});

	describe('enumRequest', () => {
		it('should call getEnumCode and update allChargesCode and filteredChargesCode', () => {
			const enumType = 'CHARGE_CODE' as any;

			component.enumRequest(enumType);

			expect(orgMgmtRequestServiceSpy.getEnumCode).toHaveBeenCalledWith(enumType);
			expect(component.allChargesCode).toEqual(mockChargesCodes);
			expect(component.filteredChargesCode).toEqual(mockChargesCodes);
		});
	});

	describe('Autocomplete functionality', () => {
		it('should filter departure airports on value change', () => {
			component.airports = mockAirports;
			component.ngOnInit();

			component.airportInfoForm.get('departureAndRequestedRouting')?.setValue('LAX');

			expect(component.filteredDepartureAirports).toEqual([{ code: 'LAX', name: 'Los Angeles International Airport' }]);
		});

		it('should filter arrival airports on value change', () => {
			component.airports = mockAirports;
			component.ngOnInit();

			component.airportInfoForm.get('airportOfDestination')?.setValue('JFK');

			expect(component.filteredArrivalAirports).toEqual([{ code: 'JFK', name: 'John F. Kennedy International Airport' }]);
		});

		it('should filter charges codes on value change', () => {
			component.allChargesCode = mockChargesCodes;
			component.ngOnInit();

			component.airportInfoForm.get('chargesCode')?.setValue('Carrier');

			expect(component.filteredChargesCode).toEqual([{ code: 'CC', name: 'Carrier Charges' }]);
		});
	});
});
