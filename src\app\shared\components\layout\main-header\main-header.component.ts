import { ChangeDetectionStrategy, Component, NO_ERRORS_SCHEMA, OnInit } from '@angular/core';
import { NavigationEnd, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { filter, tap } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OverlayModule } from '@angular/cdk/overlay';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { MatDividerModule } from '@angular/material/divider';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Org } from '@shared/models/user-profile.model';
import { AsyncPipe, CommonModule } from '@angular/common';
import { UserRole } from '@shared/models/user-role.model';
import { MatBadgeModule } from '@angular/material/badge';
import { NotificationPopupComponent } from 'src/app/modules/notification/pages/notification-popup/notification-popup.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { NotificationService } from 'src/app/modules/notification/services/notification.service';

const PRIMARY_MENUS = [
	{ id: 'shipment', urls: ['/sli', '/hawb', '/mawb', '/change-requests'] },
	{ id: 'partner', urls: ['/subscription', '/partner'] },
	{ id: 'system', urls: ['/users-mgmt'] },
];

@Component({
	selector: 'iata-main-header',
	templateUrl: './main-header.component.html',
	styleUrls: ['./main-header.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatTooltipModule,
		MatDividerModule,
		RouterLink,
		RouterLinkActive,
		TranslateModule,
		OverlayModule,
		CommonModule,
		AsyncPipe,
		MatBadgeModule,
		MatMenuModule,
		MatButtonModule,
		NotificationPopupComponent,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class MainHeaderComponent extends RolesAwareComponent implements OnInit {
	isOpen = false;
	notificationOpen = false;
	notificationNum = 0;
	readonly menuSliRoles: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
	readonly menuHawbRoles: string[] = [UserRole.FORWARDER, UserRole.CARRIER, UserRole.SHIPPER];
	readonly menuMawbRoles: string[] = [UserRole.FORWARDER, UserRole.CARRIER, UserRole.AGENT];
	readonly menuPartnerRoles: string[] = [UserRole.FORWARDER, UserRole.CARRIER, UserRole.SHIPPER];

	constructor(
		private readonly router: Router,
		private readonly userProfileService: UserProfileService,
		private readonly notificationService: NotificationService
	) {
		super();
	}

	ngOnInit(): void {
		this.router.events
			.pipe(
				filter((event) => event instanceof NavigationEnd),
				tap((event) => {
					const currentUrl = event.url;
					PRIMARY_MENUS.forEach((menu) => {
						this.activePrimaryMenu(menu, currentUrl);
					});
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();

		this.notificationService.getNotificationPerPage({ pageSize: 10, pageNum: 1 }, false).subscribe((res) => {
			this.notificationNum = res.total;
		});
	}

	activePrimaryMenu(menu: { id: string; urls: string[] }, currentUrl: string): void {
		const menuElement = document.getElementById(menu.id);

		if (menu.urls.includes(currentUrl)) {
			menuElement?.classList.add('iata-active-nav-item');
		} else {
			menuElement?.classList.remove('iata-active-nav-item');
		}
	}

	onSwitch(role: Org): void {
		this.userProfileService
			.changeRole(role.id)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((data) => {
				if (data) {
					this.userProfileService
						.getProfile(true)
						.pipe(takeUntilDestroyed(this.destroyRef))
						.subscribe(() => {
							this.isOpen = false;
							this.router.navigate(['']);
						});
				}
			});
	}

	toggleNotificationPanel(event: boolean) {
		this.notificationOpen = event;
	}
}
