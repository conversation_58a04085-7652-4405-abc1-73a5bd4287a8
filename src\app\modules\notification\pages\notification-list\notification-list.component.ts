import { Component } from '@angular/core';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NotificationComponent } from '../../components/notification/notification.component';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';

@Component({
	selector: 'orll-notification-list',
	imports: [TranslateModule, MatSlideToggleModule, NotificationComponent, MatIconModule, MatButtonModule, FormsModule],
	templateUrl: './notification-list.component.html',
	styleUrl: './notification-list.component.scss',
})
export default class NotificationListComponent {
	showUnread = false;

	markAllViewed(event: MouseEvent) {
		event.preventDefault();
		//will be implement later
	}

	onToggleChange(event: MatSlideToggleChange) {
		this.showUnread = event.checked;
	}
}
