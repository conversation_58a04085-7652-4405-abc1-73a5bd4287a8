import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HawbListObject } from '../models/hawb-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { CodeName } from '@shared/models/code-name.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { HawbSearchPayload } from '../models/hawb-search-payload.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { SearchType } from '@shared/models/search-type.model';
import { HawbCreateDto } from '../models/hawb-create.model';
import { PieceList } from '../../sli-mgmt/models/piece/piece-list.model';

@Injectable({ providedIn: 'root' })
export class HawbSearchRequestService extends ApiService implements AbstractAutocompleteService<CodeName> {
	constructor(http: HttpClient) {
		super(http);
	}

	getHawbList(pageParams: PaginationRequest, hawbSearchPayload: HawbSearchPayload): Observable<PaginationResponse<HawbListObject>> {
		return super.getData<PaginationResponse<HawbListObject>>('hawb-management', {
			...pageParams,
			...hawbSearchPayload,
		});
	}

	getHawbDetail(hawbId: string) {
		return super.getData<HawbCreateDto>(`hawb-management/info`, {
			hawbId,
		});
	}

	getPieceList(pageParams: PaginationRequest, hawbId: string): Observable<PaginationResponse<PieceList>> {
		return super.getData<PaginationResponse<PieceList>>('hawb-management/piece/list', {
			...pageParams,
			hawbId,
		});
	}

	getTotalPieceQuantity(hawbId: string) {
		return super.getData<{ totalQuantity: number; totalSlac: number; eventStatus: string }>(`hawb-management/piece-summary`, {
			hawbId,
		});
	}

	keywordQuery(keyword: string, queryKey: string): Observable<CodeName[]> {
		return super
			.getData<string[]>('hawb-management/get-keyword', {
				keyword,
				keywordType: queryKey,
			})
			.pipe(
				map((res) => {
					return res.map((it) => ({ code: it, name: it }));
				})
			);
	}

	createHawb(sliNumber: string, data: Omit<HawbCreateDto, 'sliId'>) {
		return super.postData<any>('hawb-management', { ...data, sliId: sliNumber });
	}

	updateHawb(hawbId: string, sliId: string, data: HawbCreateDto) {
		return super.updateDataPatch<any>(`hawb-management`, { ...data, sliId, id: hawbId });
	}

	getOptions(keyword: string, id?: string): Observable<CodeName[]> {
		switch (id) {
			case SearchType.SHIPPER: {
				return this.keywordQuery(keyword, 'shipper');
			}

			case SearchType.CONSIGNEE: {
				return this.keywordQuery(keyword, 'consignee');
			}

			case SearchType.HAWB: {
				return this.keywordQuery(keyword, 'hawbNumber');
			}

			case SearchType.ORIGIN: {
				return this.keywordQuery(keyword, 'origin');
			}

			case SearchType.DESTINATION: {
				return this.keywordQuery(keyword, 'destination');
			}

			default:
				return of([]);
		}
	}
}
