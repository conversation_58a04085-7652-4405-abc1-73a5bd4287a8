import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { of } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { OverlayModule } from '@angular/cdk/overlay';
import { UserProfileService } from '@shared/services/user-profile.service';
import { Org, UserProfile } from '@shared/models/user-profile.model';
import { NotificationService } from 'src/app/modules/notification/services/notification.service';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { LogisticObjType } from '@shared/models/share-type.model';
import { NotificationListObj, NotificationEventType } from 'src/app/modules/notification/models/notification.model';

// Helper function to create complete UserProfile objects for testing
function createMockUserProfile(overrides: Partial<UserProfile> = {}): UserProfile {
	return {
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'ORG_TYPE',
		userType: '1',
		menuList: [],
		permissionList: [],
		orgList: [],
		...overrides,
	};
}

const mockPagedResponse: PaginationResponse<NotificationListObj> = {
	total: 15,
	rows: [
		{
			id: '111',
			eventType: NotificationEventType.LOGISTICS_EVENT_RECEIVED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '1111',
		},
		{
			id: '222',
			eventType: NotificationEventType.LOGISTICS_OBJECT_UPDATED,
			dataType: LogisticObjType.SLI,
			companyName: 'Company A',
			logisticsObject: 'http://10.10.11.10:8090/logistics-objects/47e85903-8f7d-457e-a498-d8b2418ef45b',
			hasRead: false,
			createTime: '2025-01-01 11:30',
			waybillNumber: '2222',
		},
	],
};

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let navigationEndEvent: NavigationEnd;

	beforeEach(async () => {
		navigationEndEvent = new NavigationEnd(1, '/test', '/test');
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['changeRole', 'getProfile', 'hasSomeRole', 'isSuperUser']);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['getNotificationPerPage']);
		mockNotificationService.getNotificationPerPage.and.returnValue(of(mockPagedResponse));
		mockRouter = jasmine.createSpyObj('Router', ['navigate', 'createUrlTree', 'serializeUrl'], {
			events: of(navigationEndEvent),
		});

		await TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot(), MatIconModule, MatTooltipModule, OverlayModule],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useClass: class ActivatedRouteMock {} },
				{ provide: UserProfileService, useValue: mockUserProfileService },
				{ provide: NotificationService, useValue: mockNotificationService },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;

		// Mock document.getElementById to test activePrimaryMenu
		spyOn(document, 'getElementById').and.callFake((id: string) => {
			const element = document.createElement('div');
			element.id = id;
			return element;
		});
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnInit', () => {
		it('should subscribe to router events and call activePrimaryMenu', () => {
			spyOn(component, 'activePrimaryMenu');

			component.ngOnInit();

			// Since we're using of() with a NavigationEnd event in our mock,
			// the subscription should fire immediately
			expect(component.activePrimaryMenu).toHaveBeenCalled();
		});
	});

	describe('activePrimaryMenu', () => {
		it('should add active class when current URL is in menu URLs', () => {
			// Create a real DOM element for testing
			const mockElement = document.createElement('div');
			mockElement.id = 'shipment';
			document.body.appendChild(mockElement);

			// Update the spy to return our real element
			(document.getElementById as jasmine.Spy).and.returnValue(mockElement);

			const menu = { id: 'shipment', urls: ['/sli', '/hawb', '/mawb', '/test'] };
			const currentUrl = '/test';

			component.activePrimaryMenu(menu, currentUrl);

			expect(mockElement.classList.contains('iata-active-nav-item')).toBeTrue();

			// Clean up
			document.body.removeChild(mockElement);
		});

		it('should remove active class when current URL is not in menu URLs', () => {
			// Create a real DOM element for testing
			const mockElement = document.createElement('div');
			mockElement.id = 'shipment';
			// First add the class so we can test removal
			mockElement.classList.add('iata-active-nav-item');
			document.body.appendChild(mockElement);

			// Update the spy to return our real element
			(document.getElementById as jasmine.Spy).and.returnValue(mockElement);

			const menu = { id: 'shipment', urls: ['/sli', '/hawb', '/mawb'] };
			const currentUrl = '/test';

			component.activePrimaryMenu(menu, currentUrl);

			expect(mockElement.classList.contains('iata-active-nav-item')).toBeFalse();

			// Clean up
			document.body.removeChild(mockElement);
		});

		it('should handle null element gracefully', () => {
			// Override the spy to return null for this test
			(document.getElementById as jasmine.Spy).and.returnValue(null);

			const menu = { id: 'nonexistent', urls: ['/test'] };
			const currentUrl = '/test';

			// This should not throw an error
			expect(() => {
				component.activePrimaryMenu(menu, currentUrl);
			}).not.toThrow();

			// Reset the spy for subsequent tests
			(document.getElementById as jasmine.Spy).and.callFake((id: string) => {
				const element = document.createElement('div');
				element.id = id;
				return element;
			});
		});
	});

	it('should call changeRole on switch', () => {
		mockUserProfileService.changeRole.and.returnValue(of(false));

		fixture.detectChanges();

		component.onSwitch({ id: '1', name: 'test' } as Org);
		expect(mockUserProfileService.changeRole).toHaveBeenCalledWith('1');
	});

	it('should not call getProfile or navigate when changeRole returns false', () => {
		mockUserProfileService.changeRole.and.returnValue(of(false));

		fixture.detectChanges();

		component.onSwitch({ id: '1', name: 'test' } as Org);

		expect(mockUserProfileService.changeRole).toHaveBeenCalledWith('1');
		expect(mockUserProfileService.getProfile).not.toHaveBeenCalled();
		expect(mockRouter.navigate).not.toHaveBeenCalled();
	});

	it('should call getProfile and navigate when changeRole returns true', () => {
		const mockUserProfile = createMockUserProfile({ firstName: 'John', lastName: 'Doe' });
		mockUserProfileService.changeRole.and.returnValue(of(true));
		mockUserProfileService.getProfile.and.returnValue(of(mockUserProfile));

		fixture.detectChanges();
		component.isOpen = true; // Set initial state

		component.onSwitch({ id: '1', name: 'test' } as Org);

		expect(mockUserProfileService.changeRole).toHaveBeenCalledWith('1');
		expect(mockUserProfileService.getProfile).toHaveBeenCalledWith(true);
		expect(component.isOpen).toBe(false);
		expect(mockRouter.navigate).toHaveBeenCalledWith(['']);
	});

	it('should close dropdown and navigate to empty path after successful role change', () => {
		const mockUserProfile = createMockUserProfile({ firstName: 'John', lastName: 'Doe' });
		mockUserProfileService.changeRole.and.returnValue(of(true));
		mockUserProfileService.getProfile.and.returnValue(of(mockUserProfile));

		fixture.detectChanges();
		component.isOpen = true;

		component.onSwitch({ id: '2', name: 'another-role' } as Org);

		expect(component.isOpen).toBe(false);
		expect(mockRouter.navigate).toHaveBeenCalledWith(['']);
	});
});
