export interface SubscriptionRequest {
	topic?: string;
	subscriptionType?: string;
	ubscriptionEventType?: string;
	description?: string;
	userId?: string;
	subscriberId?: string;
	fromTab: boolean;
	groupId?: string;
}

export interface SubscriptionListObj {
	topic: string;
	topicType: string;
	createDate: string;
	approvedDate: string;
	subscriptionType: string;
	status: string;
	isRequestedBy: string;
	isRequestedAt: string;
	subscriberOrgName: string;
	orgId: string;
	id: string;
	subscriberOrgId: string;
	publisherOrgId: string;
}

export interface EventType {
	eventName: string;
	checked: string;
}

export interface SubscriptionDetailObj extends SubscriptionListObj {
	publisherOrgId: string;
	subscriptionRequestUri: string;
	orgId: string;
	status: string;
	isRequestedBy: string;
	isRequestedAt: string;
	includeSubscriptionEventType: string | null;
	permissions: string | null;
}
export interface SubscriptionDialogData {
	id: string;
	publisherOrgId: string;
	subscriberOrgId: string;
	status: string;
	orgMap: Map<string, string>;
}
export interface SubscriptionConfigurationListObj {
	id: string;
	topic: string;
	topicType: string;
	subscriptionType: string;
	subscriptionEventType: string;
	description: string;
	expiresAt: string;
	userId: string;
	orgId: string;
	subscriberId: string;
	createAt: string;
	subscriptionRequestUri: string;
}

export interface SubscriptionConfigRequest {
	topic?: string;
	subscriptionType?: string;
	subscriptionEventType?: string;
	description?: string;
	userId?: string;
	subscriberId?: string;
}

export interface SubscriptionConfigDetailObj {
	id?: string;
	topic: string;
	topicType: string;
	subscriptionType: string;
	subscriptionEventType: EventType[];
	description: string;
	expiresAt: string;
	userId: string;
	subscriberId: string;
	createAt: string;
}

export interface DictObj {
	code: string;
	name: string;
}

export enum SubcriptionRequestStatus {
	REQUEST_PENDING = 'Pending',
	REQUEST_ACCEPTED = 'Approved',
	REQUEST_REJECTED = 'Rejected',
	REQUEST_REVOKED = 'Revoked',
}
