<div class="orll-version-dialog">
	<h2 mat-dialog-title>
		<span>{{ 'mawb.event.status.history' | translate }}</span
		><mat-icon class="orll-version-dialog__clear_dialog" [matDialogClose]="''">clear_round</mat-icon>
	</h2>
	<mat-dialog-content>
		<div class="orll-version-dialog__content">
			<div class="row">
				<div class="col-4">
					{{ 'common.change.request.request.by' | translate }}:
					<span class="orll-version-dialog__fw-bold">{{ detail?.updateOrgName ?? '' }}</span>
				</div>
				<div class="col-4">
					{{ 'common.change.request.date' | translate }} :<span class="orll-version-dialog__fw-bold">{{
						detail?.actionRequestDate ?? ''
					}}</span>
				</div>
			</div>
			<div class="row">
				<table
					mat-table
					[dataSource]="dataSource"
					[trackBy]="trackById"
					aria-label="Company table"
					class="orll-version-dialog__mat commom-table">
					<ng-container matColumnDef="loType">
						<th scope="col" mat-header-cell *matHeaderCellDef class="org-name-width">
							{{ 'common.change.request.detail.table.obj' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">
							{{ row.loType ?? '' }}
						</td>
					</ng-container>
					<ng-container matColumnDef="property">
						<th scope="col" mat-header-cell *matHeaderCellDef class="org-name-width">
							{{ 'common.change.request.detail.table.property' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">
							{{ row.property ?? '' }}
						</td>
					</ng-container>
					<ng-container matColumnDef="oldValue">
						<th scope="col" mat-header-cell *matHeaderCellDef class="org-name-width">
							{{ 'common.change.request.detail.table.oldValue' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">
							{{ row.oldValue ?? '' }}
						</td>
					</ng-container>

					<ng-container matColumnDef="newValue">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="org-type-width">
							{{ 'common.change.request.detail.table.newValue' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">{{ row.newValue ?? '' }}</td>
					</ng-container>
					<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let row; columns: displayedColumns" class="orll-mawb-table__row"></tr>
				</table>
			</div>
			<div class="row">
				<div class="col-5">{{ 'common.change.request.description' | translate }}:</div>
				<div class="col-7">{{ detail?.description ?? '' }}</div>
			</div>
		</div>
	</mat-dialog-content>
	<mat-dialog-actions class="orll-version-dialog__btn">
		@if (detail?.isOwner === 'isOwner') {
			@if (detail?.requestStatus === statusChangeAction.REQUEST_PENDING) {
				<button mat-stroked-button color="primary" (click)="updateRequestStatus(statusChangeAction.REQUEST_REVOKED)">
					<mat-icon>u_turn_left</mat-icon>{{ 'common.change.request.btn.revoke' | translate }}
				</button>
				<button
					mat-button
					color="primary"
					class="orll-version-dialog__btn_ok"
					(click)="updateRequestStatus(statusChangeAction.REQUEST_REJECTED)">
					{{ 'common.change.request.btn.reject' | translate }}
				</button>

				<button mat-flat-button color="primary" (click)="updateRequestStatus(statusChangeAction.REQUEST_ACCEPTED)">
					{{ 'common.change.request.btn.approve' | translate }}<mat-icon>done</mat-icon>
				</button>
			}
		} @else if (detail) {
			@if (
				detail?.requestStatus === statusChangeAction.REQUEST_PENDING &&
				(detail.isOwner === 'isOrg' || detail.isOwner === 'isRequestBy')
			) {
				<button mat-stroked-button color="primary" (click)="updateRequestStatus(statusChangeAction.REQUEST_REVOKED)">
					<mat-icon>u_turn_left</mat-icon>{{ 'common.change.request.btn.revoke' | translate }}
				</button>
			}
			<button mat-flat-button color="primary" [matDialogClose]="''" class="orll-version-dialog__btn_ok">
				{{ 'common.dialog.ok' | translate }}<mat-icon>done</mat-icon>
			</button>
		}
	</mat-dialog-actions>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
