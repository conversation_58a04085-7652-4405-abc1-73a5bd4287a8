import { TestBed } from '@angular/core/testing';
import { SliSearchRequestService } from './sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { SLI_LIST } from '../ref-data/sli-list.data';
import { SliListObject } from '../models/sli-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { SliSearchPayload } from '../models/sli-search-payload.model';
import { HttpClient } from '@angular/common/http';
import { of, throwError } from 'rxjs';
import { environment } from '@environments/environment';

const baseUrl = environment.baseApi;

describe('SliSearchRequestService', () => {
	let service: SliSearchRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [SliSearchRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(SliSearchRequestService);
	});

	describe('getOptions', () => {
		it('should retrieve all airports when id is "airport"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(AIRPORTS.length);
					expect(response[0].code).toBe(AIRPORTS[0].code);
					expect(response[0].name).toBe(AIRPORTS[0].name);
					done();
				},
				error: done.fail,
			});
		});

		it('should filter airports by keyword', (done: DoneFn) => {
			// Arrange
			const keyword = 'LAX';
			const id = 'airport';

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(1);
					expect(response[0].code).toBe('LAX');
					expect(response[0].name).toBe('Los Angeles International Airport');
					done();
				},
				error: done.fail,
			});
		});

		it('should retrieve shipper organizations when id is "shipper"', (done: DoneFn) => {
			// Arrange
			const keyword = '';
			const id = 'shipper';

			const mockOrganizations = [
				{ name: 'Delta Air Lines', id: '1', orgType: 'SHP' },
				{ name: 'Lufthansa', id: '2', orgType: 'SHP' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			// Act
			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					// Assert
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Delta Air Lines');
					expect(response[0].name).toBe('Delta Air Lines');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called correctly
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listSliOrgName`, {
				params: jasmine.any(Object),
			});
		});
	});

	describe('getSliList', () => {
		it('should retrieve all SLI list items', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {
				existHawb: false,
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: SLI_LIST,
				total: SLI_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(SLI_LIST.length);
					expect(response.total).toBe(SLI_LIST.length);
					expect(response.rows[0].waybillNumber).toBe(SLI_LIST[0].waybillNumber);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli`, { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const sliSearchPayload: SliSearchPayload = {
				sliCodeList: ['S000920'],
				departureLocationList: ['LAX'],
				existHawb: false,
			};
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: [SLI_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].waybillNumber).toBe('S000920');
					expect(response.rows[0].departureLocation).toBe('LAX');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli`, { params: jasmine.any(Object) });
		});

		it('should handle error response for getSliList', (done: DoneFn) => {
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const errorResponse = { status: 500, message: 'Server error' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getSliList(paginationRequest, sliSearchPayload).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});
	});

	describe('getSharedSliList', () => {
		it('should retrieve shared SLI list successfully', (done: DoneFn) => {
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const mockResponse: PaginationResponse<SliListObject> = {
				rows: SLI_LIST.slice(0, 2),
				total: 2,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getSharedSliList(paginationRequest, sliSearchPayload).subscribe({
				next: (response: PaginationResponse<SliListObject>) => {
					expect(response.rows.length).toBe(2);
					expect(response.total).toBe(2);
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/share/list`, { params: jasmine.any(Object) });
					done();
				},
				error: done.fail,
			});
		});

		it('should handle error response for getSharedSliList', (done: DoneFn) => {
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const sliSearchPayload: SliSearchPayload = { existHawb: false };
			const errorResponse = { status: 403, message: 'Forbidden' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getSharedSliList(paginationRequest, sliSearchPayload).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});
	});

	describe('Additional getOptions scenarios', () => {
		it('should handle error response for getOptions with shipper', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';
			const errorResponse = { status: 400, message: 'Bad request' };

			httpClientSpy.get.and.returnValue(throwError(() => errorResponse));

			service.getOptions(keyword, id).subscribe({
				next: () => done.fail('Expected error but got success response'),
				error: (error) => {
					expect(error).toBe(errorResponse);
					done();
				},
			});
		});

		it('should handle consignee options', (done: DoneFn) => {
			const keyword = '';
			const id = 'consignee';
			const mockOrganizations = [
				{ name: 'Consignee A', id: '1', orgType: 'CNE' },
				{ name: 'Consignee B', id: '2', orgType: 'CNE' },
			];

			httpClientSpy.get.and.returnValue(of(mockOrganizations));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('Consignee A');
					expect(response[0].name).toBe('Consignee A');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listSliOrgName`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle unsupported id types by returning empty array', (done: DoneFn) => {
			const keyword = 'test';
			const id = 'unsupported';

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					expect(response).toEqual([]);
					done();
				},
				error: done.fail,
			});
		});

		it('should handle sliCode options', (done: DoneFn) => {
			const keyword = '';
			const id = 'sliCode';
			const mockResponse = { sliCodeList: ['SLI001', 'SLI002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('SLI001');
					expect(response[0].name).toBe('SLI001');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listQueryParam`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle hawbNumber options', (done: DoneFn) => {
			const keyword = '';
			const id = 'hawbNumber';
			const mockResponse = { hawbNumber: ['HAWB001', 'HAWB002'] };

			httpClientSpy.get.and.returnValue(of(mockResponse));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(2);
					expect(response[0].code).toBe('HAWB001');
					expect(response[0].name).toBe('HAWB001');
					expect(httpClientSpy.get).toHaveBeenCalledWith(`${baseUrl}/sli/listQueryParam`, {
						params: jasmine.any(Object),
					});
					done();
				},
				error: done.fail,
			});
		});

		it('should handle empty response for organization options', (done: DoneFn) => {
			const keyword = '';
			const id = 'shipper';

			httpClientSpy.get.and.returnValue(of([]));

			service.getOptions(keyword, id).subscribe({
				next: (response: CodeName[]) => {
					expect(response.length).toBe(0);
					done();
				},
				error: done.fail,
			});
		});
	});
});
