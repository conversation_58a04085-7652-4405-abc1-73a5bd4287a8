export interface MawbCreateDto {
	id?: Id;
	orgId?: string;
	sliId?: string;
	waybillPrefix: string;
	waybillNumber: string;
	houseWaybills?: string[];
	pieceIdList?: string[];
	partyList: PartyList[];
	sliPartyList?: PartyList[];
	accountingNoteList?: AccountingNote[];
	accountingInformation?: string;
	departureLocation: string;
	arrivalLocation: string;
	requestedFlight?: string;
	requestedDate?: string;
	toFirst?: string;
	toSecond?: string;
	toThird?: string;
	byFirstCarrier?: string;
	bySecondCarrier?: string;
	byThirdCarrier?: string;
	insuredAmount: Currency;
	carrierChargeCode?: string;
	weightValuationIndicator: string;
	otherChargesIndicator: string;
	declaredValueForCarriage: Currency;
	declaredValueForCustoms: Currency;
	textualHandlingInstructions: string;
	totalGrossWeight: number | null;
	serviceCode?: string | null;
	rateClassCode: string | null;
	totalVolumetricWeight: number | null;
	rateCharge: Currency;
	goodsDescription?: string;
	goodsDescriptionForRate?: string;
	otherChargeList: OtherChargeList[];
	destinationCurrencyRate?: number | null;
	destinationCharges?: Currency;
	shippingInfo?: string;
	shippingRefNo?: string;
	carrierDeclarationDate: string;
	carrierDeclarationPlace: string;
	consignorDeclarationSignature: string;
	carrierDeclarationSignature: string;
	isOwner?: string;
}

export interface Id {
	iri: boolean;
	namespace: string;
	localName: string;
	resource: boolean;
	bnode: boolean;
	triple: boolean;
	literal: boolean;
}

export interface PartyList {
	id?: string | null;
	companyName: string;
	contactName: string;
	countryCode: string;
	regionCode: string;
	locationName: string;
	cityCode: string;
	textualPostCode: string;
	phoneNumber: string;
	emailAddress: string;
	companyType: string;
	iataCargoAgentCode?: string;
	airlineCode?: string;
}

export interface OtherChargeList {
	id?: string | null;
	chargePaymentType: string;
	entitlement: string;
	otherChargeAmount?: Currency;
	otherChargeCode: string;
	chargeQuantity?: number;
	locationIndicator?: string;
	reasonDescription?: string;
}

export interface Currency {
	id?: string | null;
	currencyUnit: string;
	numericalValue: number | null;
}

export interface AccountingNote {
	id?: string | null;
	accountingNoteIdentifier: string;
	accountingNoteText: string;
}
